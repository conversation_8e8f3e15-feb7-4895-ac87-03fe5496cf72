import { useTranslations } from "next-intl";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import useEmailSubmission from "../../hooks/reset-password/use-email-submission";

interface Props {
  onSuccess: () => void;
  saveEmail: (email: string) => void; //used to store email via a state bc we need in the end to change the password
}

export default function EmailSubmission({ onSuccess, saveEmail }: Props) {
  const t = useTranslations("modules.auth.resetPassword");

  const { error, onSubmit, isPending, form } = useEmailSubmission({
    saveEmail,
    onSuccess,
  });

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="flex flex-col gap-6"
      >
        <div className="flex flex-col items-center gap-2 text-center">
          <h1 className="text-2xl font-bold">{t("title")}</h1>
          <p className="text-muted-foreground text-sm text-balance">
            {t("description")}
          </p>
        </div>
        <div className="grid gap-6">
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel htmlFor="email">{t("steps.email.label")}</FormLabel>
                <FormControl>
                  <Input
                    id="email"
                    type="email"
                    placeholder={t("steps.email.placeholder")}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          {error && (
            <p className="text-sm text-destructive text-center">{error}</p>
          )}
          <Button type="submit" className="w-full" disabled={isPending}>
            {isPending
              ? t("steps.email.button.loading")
              : t("steps.email.button.default")}
          </Button>
        </div>
      </form>
    </Form>
  );
}
