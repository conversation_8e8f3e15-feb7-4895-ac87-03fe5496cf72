"use client";

import { useTranslations } from "next-intl";
import { buttonVariants } from "@/components/ui/button";
import { CheckCircle } from "lucide-react";
import Link from "next/link";
import { cn } from "@/lib/utils";

export default function SuccessfulResetPassword() {
  const t = useTranslations("modules.auth.resetPassword");

  return (
    <div className="flex flex-col items-center gap-6 text-center">
      <CheckCircle className="h-16 w-16 text-green-500" />
      <h1 className="text-2xl font-bold">{t("steps.success.title")}</h1>
      <p className="text-muted-foreground text-sm text-balance">
        {t("steps.success.description")}
      </p>
      <Link
        href="/"
        className={cn("w-full", buttonVariants({ variant: "default" }))}
      >
        {t("steps.success.button")}
      </Link>
    </div>
  );
}
