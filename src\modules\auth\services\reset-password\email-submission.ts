import { POST } from "@/lib/http-methods";
import { CustomError } from "@/utils/custom-error";
import { AxiosError, AxiosHeaders } from "axios";

interface Params {
  email: string;
}

export async function submitResetPasswordEmail(data: Params) {
  const headers = {} as AxiosHeaders;

  try {
    await POST(
      `${process.env.BACKEND_ADDRESS}/auths/request-reset-code`,
      headers,
      data
    );

    return { status: 200, ok: true };
  } catch (error) {
    const axiosError = error as AxiosError<{ code: string; message: string }>;

    const responseStatus = axiosError.response?.status ?? 500;
    const responseCode = axiosError.response?.data.code;
    const errorMessage =
      axiosError.response?.data.message ||
      axiosError.message ||
      "Unknown error";

    throw new CustomError(errorMessage, responseStatus, responseCode);
  }
}
