"use client";
import { screenWrapperStyle } from "@/components/screen-wrapper";
import { cn } from "@/lib/utils";
import useProduct from "@/modules/catalog/hooks/products/use-product";
import Text from "@/styles/text-styles";
import { notFound } from "next/navigation";
import { useEffect } from "react";
import { useTranslations } from "use-intl";
import ProductCarousel from "./product-carousel";
import ProductDetails from "./product-details";
import ProductPurchaseConfigurator from "./product-purchase-configurator";
import ProductSimilarProductsOverview from "../../overview/product-similar-products";
import ProductFreeShippingProgressBar from "./free-shipping-progressbar";
import DeliveryTruckIcon from "@assets/icons/truck/delivery";
import usePricePointsConverter from "@/modules/points-system/hook/use-price-points-converter";
import GiftIcon from "@assets/icons/gift";
import ExpandableText from "@/components/expandable-text";

interface Props {
  slug: string;
}

export default function ProductInfoPage({ slug }: Props) {
  const { product, productIsLoading, productItem, error } = useProduct(slug);
  const t = useTranslations("productPage");
  const { points: pointsToWin, pointsPrice } = usePricePointsConverter(
    productItem?.prices[0].promotionalPrice || 0
  );

  useEffect(() => {
    if (error && error.status === 404) notFound();
  }, [error]);

  return !(product === undefined || productIsLoading) ? (
    product && productItem ? (
      <div className="w-full flex flex-col items-center regularL:space-y-6 space-y-4">
        <div
          className={cn(
            "flex regularL:flex-row flex-col regularL:justify-center regularL:space-y-0 regularL:space-x-7 space-y-4",
            screenWrapperStyle
          )}
        >
          {/* Product Images */}
          <ProductCarousel
            className="regularL:basis-[40%]"
            productItem={productItem}
          />

          <div className="regularL:basis-[60%] max-w-[600px] regularL:space-y-6 space-y-4">
            {/* Contains teh product name, refrecence prices variations and brand  */}
            <ProductDetails product={product} productItem={productItem} />
            {/* bought type and cart management */}
            <ProductPurchaseConfigurator
              productItem={{
                slug: product.slug,
                id: productItem.id,
                name: product.name,
                productId: product.id,
                image: productItem.image,
                prices: productItem.prices,
                variations: productItem.variations,
                cartQuantity: 1,
              }}
            />

            {/* Points System indicator and free shipping indicator */}
            <div className="space-y-3 pt-4">
              {/* free shipping progress bar */}
              <ProductFreeShippingProgressBar
                icon={<DeliveryTruckIcon variant="secondary" />}
                className="L:p-4 p-2 bg-primary/25 rounded-xl pb-5"
                contentClassName="L:p-0 p-0"
              />
              {/* won points from the product purchase indicator */}
              <div className="flex items-center gap-4 bg-primary/25 L:p-4 p-2 rounded-xl">
                <div className="text-primary w-12 flex justify-center">
                  <GiftIcon />
                </div>
                <p className="flex-1">
                  <Text textStyle="TS8">
                    {t.rich("purchase.pointsOffer", {
                      points: (points) => (
                        <span className="font-bold">{`${pointsToWin} ${points}`}</span>
                      ),
                      amount: (amount) => (
                        <span className="font-bold">{`${pointsPrice} ${product.items[0].prices[0].currency}`}</span>
                      ),
                    })}
                  </Text>
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Product Description Section */}
        {product.details && (
          <div className="w-full bg-primary-muted L:py-4 L:px-5 py-2 px-3 flex flex-col L:space-y-5 space-y-3 line-clamp-3">
            <Text textStyle="TS4" className="text-primary font-bold">
              {t("productDetails.details")}
            </Text>
            <ExpandableText text={product.details as string} />
          </div>
        )}

        {/* Similar Products overview section */}
        <div className={cn("w-full bg-primary-muted flex justify-center")}>
          <ProductSimilarProductsOverview
            similarProductSlug={product.slug}
            className={screenWrapperStyle}
          />
        </div>
      </div>
    ) : (
      <div className="w-full min-h-[400px] flex justify-center items-center">
        <Text textStyle="TS3" className="font-bold text-gray">
          {t("noProductsFound")}
        </Text>
      </div>
    )
  ) : (
    // Skeletons
    <div className="w-full flex flex-col items-center regularL:space-y-6 space-y-4">
      <div
        className={cn(
          "w-full flex regularL:flex-row flex-col regularL:space-y-0 regularL:space-x-7 space-y-4",
          screenWrapperStyle
        )}
      >
        {/* Product Images */}
        <ProductCarousel className="regularL:basis-[40%]" productItem={null} />

        <div className="regularL:basis-[60%] regularL:space-y-6 space-y-4">
          <ProductDetails productItem={null} />
          <ProductPurchaseConfigurator productItem={null} />
        </div>
      </div>

      <div className={cn("w-full bg-primary-muted flex justify-center")}>
        <ProductSimilarProductsOverview
          similarProductSlug={slug}
          className={screenWrapperStyle}
        />
      </div>
    </div>
  );
}
