import useTimer from "@/modules/auth/hooks/use-timer";
import { submitOTPCode } from "@/modules/auth/services/reset-password/otp-submission";
import { useTranslations } from "next-intl";
import { useEffect, useState } from "react";
import { getOTPSchema } from "../../validation/schemas/reset-password/otp";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import z from "zod";
import { CustomError } from "@/utils/custom-error";

interface Params {
  email: string;
  saveCode: (code: string) => void;

  onSuccess: () => void;
}

export default function useOTPSubmission({
  email,
  saveCode,
  onSuccess,
}: Params) {
  const { displayedTimer, startTimer } = useTimer(600);

  const t = useTranslations("modules.auth.validations");
  const errorsContent = useTranslations("modules.auth.errors");

  const [isPending, setIsPending] = useState(false);
  const [error, setError] = useState("");

  const formSchema = getOTPSchema(t);
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      otp: "",
    },
  });

  useEffect(() => {
    startTimer();
  }, []);

  async function onSubmit(values: z.infer<typeof formSchema>) {
    if (error !== "") setError("");

    try {
      await submitOTPCode({
        email: email,
        code: values.otp,
      });

      saveCode(values.otp);
      onSuccess();
    } catch (e) {
      const error = e as CustomError;

      if (error.status === 400) {
        return errorsContent("invalidCode");
      } else if (error.status === 404) {
        return errorsContent("notFound");
      } else {
        return errorsContent("technicalIssue");
      }
    } finally {
      setIsPending(false);
    }
  }

  return { onSubmit, isPending, form, error };
}
