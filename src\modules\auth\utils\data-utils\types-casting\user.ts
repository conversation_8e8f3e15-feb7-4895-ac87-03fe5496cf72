import {
  UserSignUpType,
  UserType,
  UserInResponseType,
} from "@/modules/auth/types";

export function castToUserType(userInResponse: UserInResponseType): UserType {
  return {
    email: userInResponse.email,
    name: userInResponse.name,
    isAuthenticated: true,
    role: "",
    points: userInResponse.points ? parseInt(userInResponse.points, 10) : 0,
  };
}

export function castToSignUpServerSideType(data: UserSignUpType) {
  return {
    email: data.email,
    name: `${data.firstName} ${data.lastName}`,
    password: data.password,
  };
}
