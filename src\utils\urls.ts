import { CategoryType } from "@/modules/catalog/types/categories";

export const STATIC_URLS = {
  auth: {
    signIn: "/auth/sign-in",
    signUp: "/auth/sign-up",
    resetPassword: "/auth/reset-password",
  },
  userAccount: {
    index: "/my-account",
    info: "/my-account/info",
    settings: "my-account/settings",
    addresses: "/my-account/addresses",
    orders: "/my-account/orders",
  },
  products: {
    wishlist: "/products/wishlist",
    store: "/products/filtres",
  },
  brands: {
    list: "/brands",
  },
  checkout: "/checkout",
};

export function getProductPageUrl(slug: string, productId?: string): string {
  return `/produits/${slug}`;
}

export function getSimilarProductsPage(slug: string): string {
  return `/produits/similaires/${slug}`;
}

export function getBrandPageUrl(slug: string): string {
  return `/marques/${slug}`;
}

export function getCategoryPageUrl(category: CategoryType): string {
  return `/${category.slug}`;
}

export function getSubCategoryPageUrl(
  category: CategoryType,
  subCategory: CategoryType
): string {
  return `/${category.slug}/${subCategory.slug}`;
}

export function getSubSubCategoryPageUrl(
  category: CategoryType,
  subCategory: CategoryType,
  subSubCategory: CategoryType
): string {
  return `/${category.slug}/${subCategory.slug}/${subSubCategory.slug}`;
}

export function getPromotionPageUrl(promotionSlug: string) {
  return `/promotions/${promotionSlug}`;
}

export function getOrderPageUrl(id: string) {
  return `/orders/${id}`;
}
