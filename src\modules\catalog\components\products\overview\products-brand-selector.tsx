"use client";
import { useState, useEffect } from "react";
import { BrandType } from "@/modules/catalog/types/brands";
import useProducts from "@/modules/catalog/hooks/products/use-products";
import { useTranslations } from "next-intl";
import useBrands from "@/modules/catalog/hooks/brands/use-brands";
import ProductsOverviewUI from "./ui";
import { useWindowWidth } from "@react-hook/window-size";
import { getBrandPageUrl, STATIC_URLS } from "@/utils/urls";

export function ProductsBrandSelector() {
  const { brands, brandsAreLoading } = useBrands({
    limit: 10,
  });
  const t = useTranslations("modules.catalog.products.overview");
  const screenWidth = useWindowWidth();

  // brand filtering
  const [selectedBrand, setSelectedBrand] = useState<BrandType | null>(null);
  const [brandsThatHasProducts, setBrandsThatHasProducts] = useState<
    BrandType[]
  >([]);

  //sorting brands to get brands with high number of products first
  useEffect(() => {
    if (brands && brands.length > 0) {
      setBrandsThatHasProducts(
        brands
          .filter(
            (brand) => brand.numberOfProducts && brand.numberOfProducts > 0
          )
          .sort(
            (prevBrand, brand) =>
              brand.numberOfProducts - prevBrand.numberOfProducts
          )
      );
    }
  }, [brands]);

  useEffect(() => {
    if (
      brandsThatHasProducts &&
      brandsThatHasProducts.length > 0 &&
      !selectedBrand
    ) {
      setSelectedBrand(brandsThatHasProducts[0]);
    }
  }, [brands, selectedBrand, brandsThatHasProducts]);

  const { products, productsAreLoading } = useProducts({
    limit: screenWidth < 768 ? 8 : 7,
    queryKeys: [screenWidth],

    brandSlugs: selectedBrand ? [selectedBrand.slug] : [],
  });

  const moreProductsLink = selectedBrand
    ? getBrandPageUrl(selectedBrand.slug)
    : STATIC_URLS["products"].store;

  return (
    <ProductsOverviewUI
      title={t("brandSelector.title")}
      subtitle={t("brandSelector.subtitle")}
      products={products}
      isLoading={productsAreLoading || brandsAreLoading}
      bannerImage={
        selectedBrand && selectedBrand.image
          ? {
              alt: selectedBrand.name,
              src: selectedBrand.image,
            }
          : undefined
      }
      selectedSelectorId={selectedBrand ? selectedBrand.slug : ""}
      checkMoreButton={{
        content: t("buttons.discoverPlus"),
        link: moreProductsLink,
      }}
      selectors={
        brandsThatHasProducts && brandsThatHasProducts.length > 0
          ? brandsThatHasProducts.map((brand) => ({
              name: brand.name,
              id: brand.slug,
              onClick: () => setSelectedBrand(brand),
            }))
          : []
      }
    />
  );
}
