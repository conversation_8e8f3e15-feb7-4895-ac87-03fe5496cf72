@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}
:root {
  --background: #ffffff;
  --foreground: #020817;
  --card: #ffffff;
  --card-foreground: #020817;
  --popover: #ffffff;
  --popover-foreground: #020817;
  --primary: #2563eb;
  --primary-foreground: #f8fafc;
  --secondary: #0ea5e9;
  --secondary-foreground: #f8fafc;
  --muted: #f1f5f9;
  --muted-foreground: #64748b;
  --accent: #f1f5f9;
  --accent-foreground: #020817;
  --destructive: #ef4444;
  --destructive-foreground: #f8fafc;
  --border: #e2e8f0;
  --input: #e2e8f0;
  --ring: #2563eb;
  --chart-1: #3b82f6;
  --chart-2: #0ea5e9;
  --chart-3: #a3a3a3;
  --chart-4: #f97316;
  --chart-5: #14b8a6;
  --sidebar: #f8fafc;
  --sidebar-foreground: #334155;
  --sidebar-primary: #2563eb;
  --sidebar-primary-foreground: #f8fafc;
  --sidebar-accent: #2563eb;
  --sidebar-accent-foreground: #f8fafc;
  --sidebar-border: #e2e8f0;
  --sidebar-ring: #2563eb;
  --font-sans: Roboto, sans-serif;
  --font-serif: Merriweather, serif;
  --font-mono: Roboto Mono, monospace;
  --radius: 0.5rem;
  --shadow-2xs: 0px 2px 4px 0px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0px 2px 4px 0px hsl(0 0% 0% / 0.05);
  --shadow-sm: 0px 2px 4px 0px hsl(0 0% 0% / 0.1),
    0px 1px 2px -1px hsl(0 0% 0% / 0.1);
  --shadow: 0px 2px 4px 0px hsl(0 0% 0% / 0.1),
    0px 1px 2px -1px hsl(0 0% 0% / 0.1);
  --shadow-md: 0px 2px 4px 0px hsl(0 0% 0% / 0.1),
    0px 2px 4px -1px hsl(0 0% 0% / 0.1);
  --shadow-lg: 0px 2px 4px 0px hsl(0 0% 0% / 0.1),
    0px 4px 6px -1px hsl(0 0% 0% / 0.1);
  --shadow-xl: 0px 2px 4px 0px hsl(0 0% 0% / 0.1),
    0px 8px 10px -1px hsl(0 0% 0% / 0.1);
  --shadow-2xl: 0px 2px 4px 0px hsl(0 0% 0% / 0.25);
  --tracking-normal: 0rem;
  --spacing: 0.25rem;
}

.dark {
  --background: #09090b;
  --foreground: #f8fafc;
  --card: #09090b;
  --card-foreground: #f8fafc;
  --popover: #09090b;
  --popover-foreground: #f8fafc;
  --primary: #3b82f6;
  --primary-foreground: #f8fafc;
  --secondary: #0ea5e9;
  --secondary-foreground: #09090b;
  --muted: #27272a;
  --muted-foreground: #a1a1aa;
  --accent: #2563eb;
  --accent-foreground: #f8fafc;
  --destructive: #ef4444;
  --destructive-foreground: #f8fafc;
  --border: #27272a;
  --input: #27272a;
  --ring: #3b82f6;
  --chart-1: #3b82f6;
  --chart-2: #0ea5e9;
  --chart-3: #a3a3a3;
  --chart-4: #f97316;
  --chart-5: #14b8a6;
  --sidebar: #09090b;
  --sidebar-foreground: #a1a1aa;
  --sidebar-primary: #3b82f6;
  --sidebar-primary-foreground: #f8fafc;
  --sidebar-accent: #3b82f6;
  --sidebar-accent-foreground: #f8fafc;
  --sidebar-border: #27272a;
  --sidebar-ring: #3b82f6;
  --font-sans: Roboto, sans-serif;
  --font-serif: Merriweather, serif;
  --font-mono: Roboto Mono, monospace;
  --radius: 0.5rem;
  --shadow-2xs: 0px 2px 4px 0px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0px 2px 4px 0px hsl(0 0% 0% / 0.05);
  --shadow-sm: 0px 2px 4px 0px hsl(0 0% 0% / 0.1),
    0px 1px 2px -1px hsl(0 0% 0% / 0.1);
  --shadow: 0px 2px 4px 0px hsl(0 0% 0% / 0.1),
    0px 1px 2px -1px hsl(0 0% 0% / 0.1);
  --shadow-md: 0px 2px 4px 0px hsl(0 0% 0% / 0.1),
    0px 2px 4px -1px hsl(0 0% 0% / 0.1);
  --shadow-lg: 0px 2px 4px 0px hsl(0 0% 0% / 0.1),
    0px 4px 6px -1px hsl(0 0% 0% / 0.1);
  --shadow-xl: 0px 2px 4px 0px hsl(0 0% 0% / 0.1),
    0px 8px 10px -1px hsl(0 0% 0% / 0.1);
  --shadow-2xl: 0px 2px 4px 0px hsl(0 0% 0% / 0.25);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  --font-sans: var(--font-sans);
  --font-mono: var(--font-mono);
  --font-serif: var(--font-serif);

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  --shadow-2xs: var(--shadow-2xs);
  --shadow-xs: var(--shadow-xs);
  --shadow-sm: var(--shadow-sm);
  --shadow: var(--shadow);
  --shadow-md: var(--shadow-md);
  --shadow-lg: var(--shadow-lg);
  --shadow-xl: var(--shadow-xl);
  --shadow-2xl: var(--shadow-2xl);

  --tracking-tighter: calc(var(--tracking-normal) - 0.05em);
  --tracking-tight: calc(var(--tracking-normal) - 0.025em);
  --tracking-normal: var(--tracking-normal);
  --tracking-wide: calc(var(--tracking-normal) + 0.025em);
  --tracking-wider: calc(var(--tracking-normal) + 0.05em);
  --tracking-widest: calc(var(--tracking-normal) + 0.1em);
}

body {
  letter-spacing: var(--tracking-normal);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}
