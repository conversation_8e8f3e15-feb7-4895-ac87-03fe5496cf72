import Text from "@/styles/text-styles";
import { useTranslations } from "next-intl";
import usePrices from "../hooks/use-prices";
import calculateFreeShippingProgress from "../utils/free-shipping-progress-calculation";
import { Progress } from "@/components/ui/progress";
import { HTMLAttributes } from "react";
import { cn } from "@/lib/utils";
import ShippingCart from "@assets/icons/shipping-car";

interface Props extends HTMLAttributes<"div"> {
  icon?: React.ReactNode;
  contentClassName?: string;
}

export default function FreeShippingProgressBar(props: Props) {
  const t = useTranslations("shared.cart");
  const { subtotal, minAmountForFreeShipping } = usePrices();
  const freeShippingProgress = calculateFreeShippingProgress(
    minAmountForFreeShipping,
    subtotal
  );

  const investAmount = minAmountForFreeShipping - subtotal;

  return (
    <div className={cn(" flex flex-col", props.className)}>
      <div
        className={cn(
          "rounded-lg p-1 flex items-center gap-2",
          props.contentClassName
        )}
      >
        {props.icon}
        <Text textStyle="TS7" className="font-medium">
          {investAmount > 0
            ? t.rich("description", {
                b: (chunk) => <span className="font-bold">{chunk}</span>,
                price: () => (
                  <span className="font-bold">{`${investAmount.toFixed(
                    3
                  )} DT`}</span>
                ),
              })
            : t("thankYou")}
        </Text>
      </div>

      <div className="flex justify-end relative">
        <ShippingCart
          color={freeShippingProgress === 100 ? "secondary" : "#868686"}
        />
      </div>

      <div className="px-5">
        <Progress value={freeShippingProgress} />
      </div>
    </div>
  );
}
