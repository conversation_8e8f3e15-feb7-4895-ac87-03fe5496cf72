import { GET } from "@/lib/http-methods";
import { PromotionInResponse } from "../../types";
import { castToPromtionType } from "../../utils/types-casting/promotions";
import { getBackendLocaleOnParams } from "@/utils/backend-locale";

interface Params {
  locale?: string;
}

export default async function retrievePromotionsFromServerSide({
  locale,
}: Params) {
  const params = [];

  if (locale) params.push(getBackendLocaleOnParams({ locale }));

  try {
    const endpoint = "/promotions";

    const res = await GET(`${endpoint}?${params.join("&")}`, {});

    return (res.data as PromotionInResponse[]).map((promotionInResponse) =>
      castToPromtionType(promotionInResponse)
    );
  } catch (error) {
    return [];
  }
}
