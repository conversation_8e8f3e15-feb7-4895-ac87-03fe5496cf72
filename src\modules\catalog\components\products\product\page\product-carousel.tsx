import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>rollB<PERSON> } from "@/components/ui/scroll-area";
import { cn } from "@/lib/utils";
import Image from "next/image";
import { HTMLAttributes, useEffect, useState } from "react";
import { ItemType } from "../../../../types/products";
import { Skeleton } from "@/components/ui/skeleton";
import { getPromotionPercentage } from "../../../../utils/promotion-percentage";
import Text from "@/styles/text-styles";

interface Props extends HTMLAttributes<"div"> {
  productItem: ItemType | null;
}

export default function ProductCarousel({ productItem, ...props }: Props) {
  const [productImage, setProductImage] = useState(
    "/not-found/product-image.webp"
  );
  const [selectedImage, setSelectedImage] = useState(-1);
  const promotionPercentage = productItem
    ? getPromotionPercentage(
        productItem?.prices[0].realPrice,
        productItem?.prices[0].promotionalPrice
      )
    : "0";

  useEffect(() => {
    if (productItem)
      setProductImage(
        (selectedImage > -1
          ? productItem.images[selectedImage]
          : productItem.image) || "/not-found/product-image.webp"
      );
  }, [productItem, selectedImage]);

  return productItem ? (
    <div
      className={cn(
        "w-full flex flex-col items-center space-y-4",
        props.className
      )}
    >
      <div className="max-w-[500px] w-full aspect-square border border-primary rounded-lg flex items-center justify-center relative">
        <img
          onError={() => setProductImage("/not-found/product-image.webp")}
          src={productImage}
          alt={"product-image"}
          className="w-fit max-h-full max-w-full rounded-lg"
        />
        {promotionPercentage != "0" && (
          <div className=" bg-secondary px-2 py-[2px] text-white font-bold rounded-[3px] absolute top-6 right-6">
            <Text textStyle="TS7">{`-${promotionPercentage}%`}</Text>
          </div>
        )}
      </div>
      <ScrollArea className="w-[250px] S:w-full">
        <div className="flex regularL:space-x-4 space-x-2">
          {productItem.images && productItem.images.length > 0 && (
            <button
              onClick={() => setSelectedImage(-1)}
              className={cn(
                "relative regularL:w-20 w-20 aspect-square rounded-lg overflow-hidden border border-primary-muted",
                selectedImage === -1 && "border-primary"
              )}
            >
              <Image
                src={productItem.image || "/not-found/product-image.webp"}
                alt={"product-image"}
                unoptimized
                fill
              />
            </button>
          )}
          {productItem.images.map((image, index) => (
            <button
              key={index}
              onClick={() => setSelectedImage(index)}
              className={cn(
                "relative regularL:w-20 w-20 aspect-square rounded-lg overflow-hidden border border-primary-muted",
                selectedImage === index && "border-primary"
              )}
            >
              <Image
                src={image || "/not-found/product-image.webp"}
                alt={"product-image"}
                unoptimized
                fill
              />
            </button>
          ))}
        </div>
        <ScrollBar orientation="horizontal" />
      </ScrollArea>
    </div>
  ) : (
    <div
      className={cn(
        "w-full regularL:max-w-full max-w-[500px] flex flex-col space-y-4",
        props.className
      )}
    >
      <Skeleton className="w-full aspect-square" />
      <div className="flex regularL:space-x-4 space-x-2">
        {Array.from({ length: 3 }).map((_, idx) => (
          <Skeleton
            key={idx}
            className="relative regularL:w-20 w-20 aspect-square rounded-lg overflow-hidden"
          />
        ))}
      </div>
    </div>
  );
}
