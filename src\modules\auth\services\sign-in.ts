import { POST } from "@/lib/http-methods";
import { UserSignInType } from "@/modules/auth/types";
import { AxiosError, AxiosHeaders, AxiosResponse } from "axios";
import setupTokens from "../utils/jwt/setup-tokens";
import { CustomError } from "@/utils/custom-error";

export async function signIn(data: UserSignInType) {
  const headers = {} as AxiosHeaders;

  try {
    const res: AxiosResponse = await POST(`/auths/login`, headers, data);

    const tokens = res.data as { access: string; refresh: string };

    setupTokens({ access: tokens.access, refresh: tokens.refresh });
  } catch (error) {
    const axiosError = error as AxiosError<{ code: string; message: string }>;

    const responseStatus = axiosError.response?.status ?? 500;
    const responseCode = axiosError.response?.data.code;
    const errorMessage =
      axiosError.response?.data.message ||
      axiosError.message ||
      "Unknown error";

    throw new CustomError(errorMessage, responseStatus, responseCode);
  }
}
