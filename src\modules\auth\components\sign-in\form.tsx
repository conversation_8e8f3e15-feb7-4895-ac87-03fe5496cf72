"use client";

import { useTranslations } from "next-intl";
import type React from "react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import useSignIn from "@/modules/auth/hooks/use-sign-in";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import GoogleIcon from "@assets/icons/brands/google";
import Link from "next/link";
import { STATIC_URLS } from "@/utils/urls";

export default function SignInForm({
  className,
  ...props
}: React.ComponentProps<"form">) {
  const t = useTranslations("modules.auth.signIn"); // 👈 your translation namespace
  const { error, onSubmit, isPending, form } = useSignIn();

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className={cn("flex flex-col gap-6", className)}
        {...props}
      >
        <div className="flex flex-col items-center gap-2 text-center">
          <h1 className="text-2xl font-bold">{t("title")}</h1>
          <p className="text-muted-foreground text-sm text-balance">
            {t("description")}
          </p>
        </div>

        <div className="grid gap-6">
          {/* Email Field */}
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel htmlFor="email">{t("fields.email.label")}</FormLabel>
                <FormControl>
                  <Input
                    id="email"
                    type="email"
                    placeholder={t("fields.email.placeholder")}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Password Field */}
          <FormField
            control={form.control}
            name="password"
            render={({ field }) => (
              <FormItem>
                <div className="flex items-center">
                  <FormLabel htmlFor="password">
                    {t("fields.password.label")}
                  </FormLabel>
                  <Link
                    href={STATIC_URLS["auth"].resetPassword}
                    className="ml-auto text-sm underline-offset-4 hover:underline"
                  >
                    {t("fields.password.forgot")}
                  </Link>
                </div>
                <FormControl>
                  <Input id="password" type="password" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {error && (
            <p className="text-sm text-destructive text-center">
              {t(`errors.${error}`)}
            </p>
          )}

          <Button type="submit" className="w-full" disabled={isPending}>
            {isPending ? t("actions.loggingIn") : t("actions.login")}
          </Button>

          <div className="after:border-border relative text-center text-sm after:absolute after:inset-0 after:top-1/2 after:z-0 after:flex after:items-center after:border-t">
            <span className="bg-background text-muted-foreground relative z-10 px-2">
              {t("orContinueWith")}
            </span>
          </div>

          <Button
            variant="outline"
            className="w-full bg-transparent"
            disabled={isPending}
          >
            <GoogleIcon />
            {t("actions.loginWithGoogle")}
          </Button>
        </div>

        <div className="text-center text-sm">
          {t("noAccount")}{" "}
          <Link
            href={STATIC_URLS["auth"].signUp}
            className="underline underline-offset-4"
          >
            {t("actions.signup")}
          </Link>
        </div>
      </form>
    </Form>
  );
}
