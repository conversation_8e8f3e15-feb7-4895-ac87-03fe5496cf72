import { usePathname, useRouter } from "next/navigation";
import { useCartStore } from "../store/cart-store";
import useCartVisibility from "../store/cart-visibility-store";
import CartIcon from "@assets/icons/cart";
import { useTranslations } from "next-intl";
import Text from "@/styles/text-styles";
import usePrices from "../hooks/use-prices";
import { Button } from "@/components/ui/button";

export default function TotalPriceCard() {
  const { cartItems } = useCartStore((store) => store.state);
  const { cartIsOpen } = useCartVisibility();
  const pathname = usePathname();
  const t = useTranslations("shared.cart");
  const { total } = usePrices();
  const router = useRouter();

  return !cartIsOpen &&
    cartItems.length > 0 &&
    !pathname.endsWith("paiement") ? (
    <div className="bg-white fixed z-40 bottom-0 left-0 right-0 border-t border-primary py-2 XL:px-[10%] regularL:px-16 L:px-10 px-3 flex justify-between">
      <Button
        variant="link"
        className="px-0 w-fit before:bg-primary text-primary flex items-center L:space-x-4 space-x-2"
        onClick={() => router.push("/paiement")}
      >
        <CartIcon />
        <Text textStyle="TS6" className="font-bold text-primary">
          {t("title")}
        </Text>
      </Button>

      <div className="text-primary flex flex-col space-y-1">
        <Text textStyle="TS6" className="">
          {t("total")}
        </Text>
        <Text textStyle="TS6" className="font-bold">
          {`${total.toFixed(3)} ${cartItems[0]?.prices[0]?.currency}`}
        </Text>
      </div>
    </div>
  ) : null;
}
