import { SeoMetaContentType, SeoMetaContentTypeInResponse } from "@/types";

export interface ProductType {
  id: string;
  slug: string;
  categoryIds: string[];
  brand: { slug: string; name: string; image?: string } | null;
  name: string;
  description: string | null;
  details: string | null;
  items: ItemType[];
  metaContent: SeoMetaContentType | null;
}

export interface ProductInResponseType {
  id: string;
  slug: string;
  categoryIds: string[];
  brand: { slug: string; name: string; image?: string } | null;
  name: string;
  description: string | null;
  details: string | null;
  items: ItemInResponseType[];
  metaContent: SeoMetaContentTypeInResponse;
}

export interface ItemType {
  id: string;
  barcode: string;
  reference: string;
  inStock: boolean;
  image: string;
  images: string[];
  prices: PriceType[];
  variations: VariationType[];
  promotion?: PromotionInProductType;
}

export interface ItemInResponseType {
  id: string;
  barcode: string;
  reference: string;
  inStock: boolean;
  image: string | null;
  images: string[];
  prices: PriceInResponseType[];
  variation?: VariationType[];
  promotion: PromotionInProductInResponseType;
}

export interface PriceType {
  currency: string;
  realPrice: number;
  promotionalPrice: number;
}

export interface PriceInResponseType {
  regularPrice: number;
  promotionalPrice: number;
  currency: string;
}

export interface PromotionInProductType {
  amountReduced: string;
  currency: string;
  from: string;
  to: string;
  type: string;
}

export interface PromotionInProductInResponseType {
  amountReduced: string;
  currency: string;
  from: string;
  to: string;
  type: string;
}

export interface VariationType {
  name: string;
  value: string;
}

export interface PriceRangeInResponse {
  min: number;
  max: number;
}

export interface PriceRange {
  minPrice: number;
  maxPrice: number;
}
