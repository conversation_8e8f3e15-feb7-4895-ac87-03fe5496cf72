import { useQuery } from "@tanstack/react-query";
import { usePathname } from "next/navigation";
import { useEffect, useState } from "react";
import { retrieveUserOrders } from "../services/orders/orders-extraction";
import useBackendLocale from "@/hooks/use-backend-locale";

export default function useUserOrders(limit: number) {
  const { backendLocale } = useBackendLocale();

  const pathname = usePathname();
  const [page, setPage] = useState(1);
  const [pagesNumber, setPagesNumber] = useState(1);

  const { data, isLoading, isError } = useQuery({
    queryKey: ["user-orders", page, backendLocale],
    queryFn: () => retrieveUserOrders({ page, limit, locale: backendLocale }),
    enabled: !["my-account", "my-account/info", "my-account/settings"].every(
      (disabledPathname) => pathname.endsWith(disabledPathname)
    ), //data in those pages should not be fetched
  });

  //updating pages number once data is fetched
  useEffect(() => {
    if (data)
      setPagesNumber(
        data?.pagination?.totalPages ? data?.pagination?.totalPages : 1
      );
  }, [data]);

  return {
    orders: data ? data.orders : null,
    ordersAreLoading: isLoading,
    pagesNumber,
    currentPage: page,
    setPage,
  };
}
