import { PATCH } from "@/lib/http-methods";
import { AxiosError } from "axios";
import { refreshToken } from "./refresh-token";
import extractJWTokens from "@/modules/auth/utils/jwt/extract-tokens";
import { CustomError } from "@/utils/custom-error";

interface Params {
  name: string;
}

export async function changeNameOnServerSide(data: Params) {
  const { access } = extractJWTokens();
  const headers = {
    Authorization: `Bearer ${access}`,
  };

  try {
    await PATCH(`${process.env.BACKEND_ADDRESS}/users/me`, headers, data);
  } catch (error) {
    const axiosError = error as AxiosError<{ code: string; message: string }>;

    const responseStatus = axiosError.response?.status ?? 500;
    const responseCode = axiosError.response?.data.code;
    const errorMessage =
      axiosError.response?.data.message ||
      axiosError.message ||
      "Unknown error";

    //unauthorzied user
    if (responseStatus === 401) {
      const res = await refreshToken(() => changeNameOnServerSide(data));
      return res;
    } else {
      throw new CustomError(errorMessage, responseStatus, responseCode);
    }
  }
}
