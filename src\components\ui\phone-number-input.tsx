"use client";

import type { HTMLAttributes } from "react";
import { cn } from "@/lib/utils";
import { Input } from "@/components/ui/input"; // Import shadcn Input component

interface PhoneNumberPropsType extends HTMLAttributes<HTMLDivElement> {
  code: string;
  inputClassName?: string;
  inputName: string;
  primaryTheme?: boolean;
  value?: string; // Add value prop for controlled input
  onChange?: (event: React.ChangeEvent<HTMLInputElement>) => void; // Add onChange prop
}

export default function PhoneNumberInput({
  code,
  className,
  inputName,
  inputClassName,
  primaryTheme = true,
  value,
  onChange,
  ...props
}: PhoneNumberPropsType) {
  return (
    <div
      className={cn(
        "flex h-10 items-center rounded-lg border", // Adjusted rounded and border
        "bg-background/55", // Adjusted background opacity
        className
      )}
      dir="ltr"
      {...props}
    >
      <span
        className={cn(
          "pr-3 text-base font-medium", // Replaced Text component with span and Tailwind classes
          "border-r", // Border right for separator
          inputClassName // Apply inputClassName to the code span as well if desired
        )}
      >
        {code}
      </span>
      <Input
        name={inputName}
        className={cn(
          "flex-1 h-full px-2 text-base", // Shadcn Input specific overrides
          inputClassName
        )}
        type="tel" // Use type="tel" for phone numbers
        value={value}
        onChange={onChange}
      />
    </div>
  );
}
