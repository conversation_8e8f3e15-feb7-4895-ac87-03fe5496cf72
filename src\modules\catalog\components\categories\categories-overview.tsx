"use client";
import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/lib/utils";
import { useTranslations } from "next-intl";
import useCategories from "../../hooks/categories/use-categories";
import CategoryContainer from "./category-container";

export default function CategoriesOverview() {
  const t = useTranslations("modules.catalog.categories.overview");
  const { categories, categoriesAreLoading } = useCategories();

  return !(categories === undefined || categoriesAreLoading) ? (
    categories && (
      <section className={cn("flex w-full flex-col space-y-4")}>
        <div className="mb-4 text-center sm:mb-6 lg:mb-8">
          <h2 className="text-xl font-bold sm:text-2xl md:text-3xl">
            {t("title")}
          </h2>
          <p className="mx-auto mt-1 max-w-2xl text-xs sm:mt-2 sm:text-sm md:text-base text-muted-foreground">
            {t("subtitle")}
          </p>
        </div>

        <div className="relative">
          {/* Mobile scrollable view */}
          <div className="flex w-full gap-4 overflow-x-auto pb-6 sm:gap-6 md:hidden">
            <div className="mx-auto flex gap-4 sm:gap-6">
              {categories.map((category, idx) => (
                <div key={idx} className="w-[100px] flex-shrink-0 sm:w-[120px]">
                  <CategoryContainer category={category} />
                </div>
              ))}
            </div>
          </div>

          {/* Desktop grid view */}
          <div className="hidden grid-cols-4 gap-4 md:grid md:gap-6 lg:grid-cols-8 lg:gap-8">
            {categories.map((category, idx) => (
              <div key={idx}>
                <CategoryContainer category={category} />
              </div>
            ))}
          </div>
        </div>
      </section>
    )
  ) : (
    <section
      className={cn(
        "flex w-full flex-col items-center space-y-8 px-4 sm:px-6 md:px-8"
      )}
    >
      <div className="mb-4 w-full text-center sm:mb-6">
        <h2 className="w-full">
          <Skeleton className="mx-auto h-8 w-32 sm:h-10 sm:w-40" />
        </h2>
        <Skeleton className="mx-auto mt-1 h-4 w-3/4 max-w-2xl sm:mt-2 sm:h-5" />
      </div>

      <div className="relative w-full">
        {/* Mobile skeleton */}
        <div className="flex w-full gap-4 overflow-x-auto pb-6 sm:gap-6 md:hidden hide-scrollbar">
          <div className="mx-auto flex gap-4 sm:gap-6">
            {Array.from({ length: 8 }).map((_, idx) => (
              <div
                key={idx}
                className="flex w-[100px] flex-shrink-0 flex-col gap-2 sm:w-[120px]"
              >
                <Skeleton className="aspect-square w-full rounded-2xl" />
                <Skeleton className="mx-auto h-4 w-3/4 sm:h-5" />
                <Skeleton className="mx-auto hidden h-3 w-1/2 sm:block sm:h-4" />
              </div>
            ))}
          </div>
        </div>

        {/* Desktop skeleton grid */}
        <div className="hidden grid-cols-4 gap-4 md:grid md:gap-6 lg:grid-cols-8 lg:gap-8">
          {Array.from({ length: 8 }).map((_, idx) => (
            <div key={idx} className="flex flex-col gap-2">
              <Skeleton className="aspect-square w-full rounded-2xl" />
              <Skeleton className="mx-auto h-4 w-3/4 md:h-5" />
              <Skeleton className="mx-auto h-3 w-1/2 md:h-4" />
            </div>
          ))}
        </div>

        {/* Pagination indicators for mobile */}
        <div className="absolute bottom-0 left-0 right-0 flex justify-center gap-1 md:hidden">
          <div className="h-1.5 w-1.5 rounded-full bg-primary opacity-70"></div>
          <div className="h-1.5 w-1.5 rounded-full bg-gray-300"></div>
          <div className="h-1.5 w-1.5 rounded-full bg-gray-300"></div>
        </div>
      </div>
    </section>
  );
}
