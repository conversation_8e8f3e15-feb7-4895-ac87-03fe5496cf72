import { useState } from "react";

import { useTranslations } from "next-intl";
import { useForm } from "react-hook-form";
import z from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { CustomError } from "@/utils/custom-error";
import { getEmailSchema } from "../../validation/schemas/reset-password/email";
import { submitResetPasswordEmail } from "../../services/reset-password/email-submission";

interface Params {
  onSuccess: () => void;
  saveEmail: (email: string) => void; //used to store email via a state bc we need in the end to change the password
}

export default function useEmailSubmission({ onSuccess, saveEmail }: Params) {
  const t = useTranslations("modules.auth.validations");
  const errorsContent = useTranslations("modules.auth.errors");

  const [isPending, setIsPending] = useState(false);
  const [error, setError] = useState("");

  const formSchema = getEmailSchema(t);
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: "",
    },
  });

  async function onSubmit(values: z.infer<typeof formSchema>) {
    if (error !== "") setError("");

    try {
      await submitResetPasswordEmail({
        email: values.email,
      });

      saveEmail(values.email);
      onSuccess();
    } catch (e) {
      const error = e as CustomError;

      if (error.status === 400) {
        return errorsContent("invalidData");
      } else if (error.status === 404) {
        return errorsContent("notFound");
      } else {
        return errorsContent("technicalIssue");
      }
    } finally {
      setIsPending(false);
    }
  }

  return { onSubmit, isPending, form, error };
}
