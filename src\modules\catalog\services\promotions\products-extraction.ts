import { GET } from "@/lib/http-methods";
import { PaginationType } from "@/types";
import { ProductInResponseType } from "../../types/products";
import { castToProductType } from "../../utils/types-casting/products";
import { CustomError } from "@/utils/custom-error";
import { AxiosError } from "axios";
import { getBackendLocaleOnParams } from "@/utils/backend-locale";

interface Params {
  page: number;
  limit: number;
  slug: string;
  locale?: string;
}

export async function retrievePromotionProductsFromServerSide({
  page,
  limit,
  slug,
  locale,
}: Params) {
  try {
    const params = [`page=${page}`, `limit=${limit}`];

    if (locale) params.push(getBackendLocaleOnParams({ locale }));

    const res = await GET(
      `promotions/${slug}/products?${params.join("&")}`,
      {}
    );

    return {
      pagination: res.data.pagination as PaginationType,
      products: (res.data.data as ProductInResponseType[]).map(
        (productInResponse) => castToProductType(productInResponse)
      ),
    };
  } catch (error) {
    const errorResponse = (error as AxiosError).response as {
      data: { message: string };
      status: number;
    };

    throw new CustomError(
      errorResponse.data?.message as string,
      errorResponse.status as number
    );
  }
}
