"use client";
import { type HTMLAttributes } from "react";
import type { ProductsSectionsVariant } from "../../../types";
import { getCriteriaBasedOnProductsVariant } from "@/modules/catalog/utils/criteria-based-on-variant";
import { useTranslations } from "next-intl";
import useProducts from "@/modules/catalog/hooks/products/use-products";
import ProductsOverviewUI from "./ui";
import { useWindowWidth } from "@react-hook/window-size";
import { getSimilarProductsPage } from "@/utils/urls";

interface Props extends HTMLAttributes<"html"> {
  maxProductsNumber?: number;
  similarProductSlug: string;
  similarProductName?: string;
  variant?: ProductsSectionsVariant;
}

export default function ProductSimilarProducts({
  maxProductsNumber = 4,
  variant = "default",
  ...props
}: Props) {
  const t = useTranslations("modules.catalog.products.overview");
  const screenWidth = useWindowWidth();

  const { products, productsAreLoading } = useProducts({
    limit: maxProductsNumber,

    queryKeys: [screenWidth],
    criteria: getCriteriaBasedOnProductsVariant(variant),
    similarProductSlug: props.similarProductSlug,
  });

  const moreProductsLink = getSimilarProductsPage(props.similarProductSlug);

  return (
    <ProductsOverviewUI
      title={t("recommendations.title")}
      subtitle={t("recommendations.subtitle")}
      products={products}
      isLoading={productsAreLoading}
      checkMoreButton={{
        content: t("buttons.discoverPlus"),
        link: moreProductsLink,
      }}
      className={props.className}
    />
  );
}
