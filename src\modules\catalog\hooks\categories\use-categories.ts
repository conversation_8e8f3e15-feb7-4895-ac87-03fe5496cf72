import { useQuery } from "@tanstack/react-query";
import { CategoryType } from "../../types/categories";
import { retrieveCategoriesFromServerSide } from "../../services/categories/categories-extraction";
import useBackendLocale from "@/hooks/use-backend-locale";

export default function useCategories() {
  const { backendLocale } = useBackendLocale();

  const { data, isLoading, isError } = useQuery<CategoryType[] | null>({
    queryKey: ["categories", backendLocale],
    queryFn: () => retrieveCategoriesFromServerSide({ locale: backendLocale }),
  });

  return {
    categories: data,
    categoriesAreLoading: isLoading,
    categoriesError: isError,
  };
}
