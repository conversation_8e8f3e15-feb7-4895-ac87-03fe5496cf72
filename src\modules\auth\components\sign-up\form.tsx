"use client";

import { useTranslations } from "next-intl";
import type React from "react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import Link from "next/link";
import useSignUp from "../../hooks/use-sign-up";
import GoogleIcon from "@assets/icons/brands/google";
import { STATIC_URLS } from "@/utils/urls";

export default function SignUpForm({
  className,
  ...props
}: React.ComponentProps<"form">) {
  const t = useTranslations("modules.auth.signUp"); // 👈 your translation namespace
  const { error, onSubmit, isPending, form } = useSignUp();

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className={cn("flex flex-col gap-6", className)}
        {...props}
      >
        <div className="flex flex-col items-center gap-2 text-center">
          <h1 className="text-2xl font-bold">{t("title")}</h1>
          <p className="text-muted-foreground text-sm text-balance">
            {t("description")}
          </p>
        </div>
        <div className="grid gap-6">
          {/* First Name Field */}
          <FormField
            control={form.control}
            name="firstName"
            render={({ field }) => (
              <FormItem>
                <FormLabel htmlFor="firstName">
                  {t("fields.firstName.label")}
                </FormLabel>
                <FormControl>
                  <Input
                    id="firstName"
                    placeholder={t("fields.firstName.placeholder")}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Last Name Field */}
          <FormField
            control={form.control}
            name="lastName"
            render={({ field }) => (
              <FormItem>
                <FormLabel htmlFor="lastName">
                  {t("fields.lastName.label")}
                </FormLabel>
                <FormControl>
                  <Input
                    id="lastName"
                    placeholder={t("fields.lastName.placeholder")}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Email Field */}
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel htmlFor="email">{t("fields.email.label")}</FormLabel>
                <FormControl>
                  <Input
                    id="email"
                    type="email"
                    placeholder={t("fields.email.placeholder")}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Password Field */}
          <FormField
            control={form.control}
            name="password"
            render={({ field }) => (
              <FormItem>
                <div className="flex items-center">
                  <FormLabel htmlFor="password">
                    {t("fields.password.label")}
                  </FormLabel>
                  <Link
                    href={STATIC_URLS["auth"].resetPassword}
                    className="ml-auto text-sm underline-offset-4 hover:underline"
                  >
                    {t("fields.password.forgot")}
                  </Link>
                </div>
                <FormControl>
                  <Input id="password" type="password" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Global error display from useSignUp hook */}
          {error && (
            <p className="text-sm text-destructive text-center">{error}</p>
          )}

          <Button type="submit" className="w-full" disabled={isPending}>
            {isPending ? t("actions.signingUp") : t("actions.signup")}
          </Button>
          <div className="after:border-border relative text-center text-sm after:absolute after:inset-0 after:top-1/2 after:z-0 after:flex after:items-center after:border-t">
            <span className="bg-background text-muted-foreground relative z-10 px-2">
              {t("orContinueWith")}
            </span>
          </div>
          {/* Removed Google and GitHub buttons as per request */}
          <Button
            variant="outline"
            className="w-full bg-transparent"
            disabled={isPending}
          >
            <GoogleIcon />

            {/* No icon here as GoogleIcon was removed */}
            {t("actions.signUpWithGoogle")}
          </Button>
        </div>
        <div className="text-center text-sm">
          {t("hasAccount")}
          <Link
            href={STATIC_URLS["auth"].signIn}
            className="underline underline-offset-4"
          >
            {t("actions.login")}
          </Link>
        </div>
      </form>
    </Form>
  );
}
