import { toCamelCase } from "@/utils/text-transformer";
import { CategoryInResponseType, CategoryType } from "../../types/categories";
import { castToMetaContentType } from "@/utils/types-casting/meta-content";

export function castToCategoryType(
  categoryInResponse: CategoryInResponseType
): CategoryType {
  return {
    metaContent: castToMetaContentType(categoryInResponse.metaContent),
    slug: categoryInResponse.slug,
    name: toCamelCase(categoryInResponse.name),
    description: categoryInResponse.description as string,
    numberOfProducts: Number(categoryInResponse.numberOfProducts),
    id: categoryInResponse.id,
    image: categoryInResponse.image
      ? `${process.env.BACKEND_ADDRESS}${categoryInResponse.image}`
      : null,
    subCategories: categoryInResponse.subCategories
      ? categoryInResponse.subCategories.map((subCategory) =>
          castToCategoryType(subCategory)
        )
      : [],
  };
}
