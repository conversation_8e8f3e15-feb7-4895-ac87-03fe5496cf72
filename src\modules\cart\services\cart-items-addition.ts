import { POST } from "@/lib/http-methods";
import { refreshToken } from "@/modules/auth/services/refresh-token";
import extractJWTokens from "@/modules/auth/utils/jwt/extract-tokens";
import { AxiosError } from "axios";
import { PostedCartItemType } from "../types/products";
import { CustomError } from "@/utils/custom-error";

export async function addCartItemsOnServerSide(items: PostedCartItemType[]) {
  const { access } = extractJWTokens();
  const header = {
    Authorization: `Bearer ${access}`,
  };

  try {
    const itemsToAddToCart = items.map((item) => ({
      productItemId: item.id,
      quantity: item.quantity,
    }));
    await POST("/carts/products/register/many", header, {
      items: itemsToAddToCart,
    });

    return { ok: true };
  } catch (error) {
    const axiosError = error as AxiosError;

    if (axiosError.response?.status == 401) {
      const res = await refreshToken(() => addCartItemsOnServerSide(items));

      if (!res) throw new CustomError("Unauthorized", 401);
    } else if (axiosError.response?.status == 404) {
      throw new CustomError("Product Not Found!", 404);
    } else throw new CustomError("Server Error!", 500);
  }
}
