import { GET } from "@/lib/http-methods";
import { castToCategoryType } from "../../utils/types-casting/categories";
import { getBackendLocaleOnParams } from "@/utils/backend-locale";

interface Params {
  slug: string;
  locale?: string;
}

export async function retrieveCategoryFromServerSide({ slug, locale }: Params) {
  const params = [];

  if (locale) params.push(getBackendLocaleOnParams({ locale }));

  try {
    const res = await GET(`/categories/${slug}?${params.join("&")}`, {});

    return castToCategoryType(res.data);
  } catch (error) {
    return null;
  }
}
