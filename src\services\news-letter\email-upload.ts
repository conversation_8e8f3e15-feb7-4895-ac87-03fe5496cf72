import { POST } from "@/lib/http-methods";
import { CustomError } from "@/utils/custom-error";

interface Params {
  email: string;
}

export default async function uploadEmailToServerSide({ email }: Params) {
  try {
    const headers = {
      Authorization: `Bearer`,
      "Content-Type": "application/json",
    };

    const endpoint = "/newsletter/emails/register";

    const res = await POST(
      `${process.env.BACKEND_ADDRESS}${endpoint}`,
      headers,
      { email }
    );

    return res.data;
  } catch (error) {
    if (error instanceof CustomError) {
      throw error;
    }
    throw new CustomError("Server Error!", 500);
  }
}
