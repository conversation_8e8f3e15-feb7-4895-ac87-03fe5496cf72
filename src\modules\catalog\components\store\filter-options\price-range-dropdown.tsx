import { Button } from "@/components/ui/button";
import { Slider } from "@/components/ui/slider";
import useMinMaxPrice from "@/modules/catalog/hooks/products/use-min-max-price";
import useCurrency from "@/modules/catalog/hooks/use-currency";
import { useProductsFilteringStore } from "@/modules/catalog/store/products-filter";
import Text from "@/styles/text-styles";
import { useTranslations } from "next-intl";
import FilterChoiceContainer from "./filter-choice-container";

export default function PriceRangeDropdown() {
  const t = useTranslations("filtersPage");

  const { priceRange, setPriceRange, applyPriceRangeFiltering } =
    useProductsFilteringStore();
  const { prices: minMaxPrices } = useMinMaxPrice();
  const { currency } = useCurrency();

  const handleSliderChange = (value: [number, number]) => {
    setPriceRange(value);
  };

  return (
    <FilterChoiceContainer title={t.raw("priceHeader")}>
      <div className="flex flex-col space-y-4">
        <div className="space-y-3">
          <Slider
            value={priceRange}
            min={minMaxPrices[0]}
            max={minMaxPrices[1]}
            step={1}
            onValueChange={handleSliderChange}
            className="w-full mt-4"
          />
          <div className="flex justify-between mt-2 text-sm">
            <Text textStyle="TS6"> {`${currency} ${minMaxPrices[0]}`}</Text>
            <Text textStyle="TS6"> {`${currency} ${minMaxPrices[1]}`}</Text>
          </div>
        </div>
        {/* <div className="flex flex-col space-y-2 items-center">
          <div className="flex gap-3 items-center">
            <div className=" flex items-center w-full border border-primary rounded-lg ">
              <Text textStyle="TS7" className="pl-1">
                {currency}
              </Text>
              <Input
                name="minimum"
                type="number"
                onChange={handleInputMin}
                className={cn(" flex-1 py-5 px-1", TextStyle["TS7"])}
                value={priceRange[0]}
              />
            </div>
            <Text textStyle="TS6" className="text-gray">
              {t.raw("to")}
            </Text>
            <div className=" flex items-center w-full border border-primary rounded-lg ">
              <Text textStyle="TS7" className="pl-1">
                {currency}
              </Text>
              <Input
                name="maximum"
                type="number"
                onChange={handleInputMax}
                className={cn(" flex-1 py-5 px-1", TextStyle["TS7"])}
                value={priceRange[1]}
              />
            </div>
          </div>
        </div> */}
        <div className="flex regularL:flex-row regularL:justify-between flex-col gap-2">
          <div className="flex gap-3 items-center">
            <Text textStyle="TS6" className="">
              {`${t.raw("priceHeader")}: `}
            </Text>
            {priceRange.length > 1 && (
              <p>
                <Text textStyle="TS7" className="font-bold text-primary">
                  {`${priceRange[0]} ${currency} `}
                </Text>
                -
                <Text textStyle="TS7" className="font-bold text-primary">
                  {` ${priceRange[1]} ${currency}`}
                </Text>
              </p>
            )}
          </div>
          <Button
            className="w-fit rounded-md py-1 h-fit px-2 border bg-primary-light text-primary"
            onClick={applyPriceRangeFiltering}
          >
            <Text textStyle="TS6">{t.raw("apply")}</Text>
          </Button>
        </div>
      </div>
    </FilterChoiceContainer>
  );
}
