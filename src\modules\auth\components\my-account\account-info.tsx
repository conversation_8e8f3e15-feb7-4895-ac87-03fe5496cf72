"use client";
import { Skeleton } from "@/components/ui/skeleton";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"; // Import Form components
import { useTranslations } from "next-intl";
import useNameChangement from "@/modules/auth/hooks/account-management/use-name-changement";
import { cn } from "@/lib/utils";
import useUserStore from "@/modules/auth/store/user-store";
import { Label } from "@/components/ui/label";

export default function AccountInfo() {
  const { user, isLoading } = useUserStore((store) => store);
  const { onSubmit, form, isPending, nameChanged } = useNameChangement(); // Destructure form from hook

  const t = useTranslations("accountPage.accountInfo");

  return !isLoading ? (
    <div className="flex flex-col space-y-7">
      <h2 className="text-xl font-bold text-primary">{t("title")}</h2>
      <Form {...form}>
        {" "}
        {/* Wrap with Form component */}
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="flex flex-col space-y-7 max-w-lg"
        >
          {form.formState.errors.root?.serverError && (
            <p className="text-sm text-destructive text-center">
              {t(`warnings.${form.formState.errors.root.serverError.message}`)}
            </p>
          )}
          <div className="space-y-7">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-base text-primary">
                    {t("name")}
                  </FormLabel>
                  <FormControl>
                    <Input
                      className={cn("text-primary")}
                      type="text"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex flex-col space-y-3 w-full rounded-sm">
              <Label className="text-base text-primary">{t("email")}</Label>
              <Input
                value={user?.email || ""}
                disabled
                className="border border-gray-200 p-3 text-primary"
              />
            </div>
            <Button
              type="submit" // Set type to submit
              className="bg-primary text-primary-foreground rounded-xl h-11 w-fit md:min-w-[350px] lg:min-w-[450px] hover:bg-primary/90"
              disabled={isPending} // Disable button during submission
            >
              <span className="text-base font-medium">
                {isPending
                  ? "Saving..."
                  : nameChanged
                  ? t("buttons.changementConfirmed")
                  : t("buttons.save")}
              </span>
            </Button>
          </div>
        </form>
      </Form>
    </div>
  ) : (
    <div className="flex flex-col space-y-7 p-6 bg-white rounded-lg shadow-md">
      <Skeleton className="w-40 h-6" />
      <div className="grid grid-cols-1 gap-4">
        <Skeleton className="w-full h-10" />
        <Skeleton className="w-full h-10" />
      </div>
      <Skeleton className="w-full h-10" />
      <Skeleton className="w-full h-10" />
      <Skeleton className="w-full h-10" />
      <Skeleton className="w-full h-12" />
    </div>
  );
}
