import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/sheet";
import CartIcon from "@assets/icons/cart";
import ProductContainer from "@/modules/cart/components/product-container";
import Text from "@/styles/text-styles";
import { useTranslations } from "next-intl";
import { Button } from "@/components/ui/button";
import useCartVisibility from "../store/cart-visibility-store";
import { useCart } from "../store/cart-store";
import NotificationPoint from "@/components/ui/notification-point";
import { useRouter } from "next/navigation";
import usePrices from "../hooks/use-prices";
import FreeShippingProgressBar from "./free-shipping-progressbar";
import SimilarProducts from "./similar-products";
import useCurrency from "@/modules/catalog/hooks/use-currency";

export default function Cart() {
  const t = useTranslations("shared.cart");
  const { cartItems } = useCart();
  const { cartIsO<PERSON>, set<PERSON><PERSON><PERSON>s<PERSON><PERSON> } = useCartVisibility();
  const router = useRouter();
  const { total, subtotal, shipping } = usePrices();
  const { currency } = useCurrency();

  return (
    <>
      <Sheet open={cartIsOpen} onOpenChange={(open) => setCartIsOpen(open)}>
        <SheetTrigger asChild className="pl-2">
          <Button
            size="icon"
            variant="ghost"
            className="px-2 py-0 relative text-primary"
          >
            <CartIcon />
            {cartItems.length > 0 && (
              <NotificationPoint className="top-0 right-1" />
            )}
          </Button>
        </SheetTrigger>
        <SheetContent className="p-0 rounded-[15px]  overflow-hidden top-5 h-[calc(100%-40px)]  M:max-w-sm L:max-w-lg right-5 M:left-auto left-5 flex flex-col justify-between">
          <SheetHeader className="L:px-6 pt-6 px-4">
            <SheetTitle className="flex items-center space-x-2 text-primary ">
              <CartIcon />
              <Text textStyle="TS5" className="font-bold text-gray-dark">
                {t("title")}
              </Text>
            </SheetTitle>

            <FreeShippingProgressBar className="space-y-3" />
          </SheetHeader>
          <h2 className="L:px-6 px-4">
            <Text textStyle="TS7" className="font-bold">
              {t("cartProductsTitle")}
            </Text>
          </h2>
          {cartItems.length > 0 ? (
            <div className="flex-1 overflow-y-auto pt-1 scrollbar-thin scrollbar-track-transparent scrollbar-thumb-primary">
              <div className="flex flex-col space-y-4 ">
                {cartItems.map((item) => (
                  <div key={item.id} className="pr-4 L:px-6 px-4">
                    <ProductContainer productItem={item} />
                  </div>
                ))}
                <SimilarProducts />
              </div>
            </div>
          ) : (
            <div className="flex-1 flex items-center justify-center">
              <Text textStyle="TS7" className="text-primary">
                {t("emptyCart")}
              </Text>
            </div>
          )}

          <div>
            {cartItems.length > 0 && (
              <SheetFooter className="py-3 pr-4 L:px-6 px-4 border-t border-primary bg-white shadow-lg flex flex-col space-y-4 text-primary">
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <Text textStyle="TS7" className="font-medium">
                      {t("subtotal")}
                    </Text>
                    <Text textStyle="TS7">{`${subtotal.toFixed(
                      3
                    )} ${currency}`}</Text>
                  </div>
                  <div className="flex justify-between">
                    <Text textStyle="TS7" className="font-medium">
                      {t("shipping")}
                    </Text>
                    <Text textStyle="TS7">{`${shipping.toFixed(
                      3
                    )} ${currency}`}</Text>
                  </div>
                </div>
                <div className="flex justify-between font-bold">
                  <Text textStyle="TS6" className="">
                    {t("total")}
                  </Text>
                  <Text textStyle="TS6">{`${total.toFixed(
                    3
                  )} ${currency}`}</Text>
                </div>
                <Button
                  className="w-full rounded-md bg-primary"
                  onClick={() => {
                    router.push("/paiement");
                    setCartIsOpen(false);
                  }}
                >
                  {t("checkout")}
                </Button>
              </SheetFooter>
            )}
          </div>
        </SheetContent>
      </Sheet>
    </>
  );
}
