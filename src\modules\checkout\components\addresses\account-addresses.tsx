"use client";
import { Skeleton } from "@/components/ui/skeleton";
import { useTranslations } from "next-intl";
import useAddresses from "@/modules/checkout/hooks/addresses/use-addresses";
import useAddressDeletion from "@/modules/checkout/hooks/addresses/use-address-deletion";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"; // Import shadcn AlertDialog components
import { cn } from "@/lib/utils";
import AddressCreationForm from "@/modules/checkout/components/addresses/form/address-creation-form";
import AddressManagementContainer from "./container/adress-management";

export default function AccountAddresses() {
  const t = useTranslations("accountPage.accountAdress");
  const { addresses, addressesAreLoading } = useAddresses();
  const {
    deleteAddress,
    deletionIsLoading,
    deletionPopUpIsOpen,
    cancelDeletion,
    confirmAddressDeletion,
    warning: deletionWarning,
  } = useAddressDeletion();

  return (
    <div className="flex flex-col space-y-7">
      {addressesAreLoading ? (
        <>
          <Skeleton className="w-[200px] h-8" />{" "}
          {/* Adjusted skeleton height */}
          <div className="flex flex-col border  rounded-lg px-5 py-3">
            {" "}
            {/* Adjusted border and padding */}
            {Array.from({ length: 3 }).map((_, idx) => (
              <div
                key={idx}
                className={cn("py-3 flex flex-col space-y-2", {
                  "border-b ": idx !== 2, // Adjusted border color
                })}
              >
                <Skeleton className="w-[200px] h-6" />
                <div className="flex justify-between space-x-2">
                  <Skeleton className="w-[150px] h-6" />
                  <Skeleton className="w-[80px] h-6" />
                </div>
              </div>
            ))}
          </div>
        </>
      ) : (
        addresses &&
        addresses.length > 0 && (
          <>
            <h2 className="text-xl font-bold text-primary">{t("title")}</h2>
            <div className="flex flex-col border rounded-lg px-5 py-3">
              {addresses.map((address, idx) => (
                <AddressManagementContainer
                  key={address.id} // Use unique ID for key
                  address={address}
                  onDelete={deleteAddress}
                  bottomDelimter={idx !== addresses.length - 1}
                />
              ))}
            </div>
          </>
        )
      )}
      <h2 className="text-xl font-bold text-primary">{t("subtitle")}</h2>
      <AddressCreationForm />

      <AlertDialog open={deletionPopUpIsOpen} onOpenChange={cancelDeletion}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{t("deletionForm.title")}</AlertDialogTitle>
            <AlertDialogDescription>
              {t("deletionForm.description")}
            </AlertDialogDescription>
          </AlertDialogHeader>
          {deletionWarning && (
            <p className="text-sm text-destructive text-center">
              {t(`warnings.${deletionWarning}`)}
            </p>
          )}
          <AlertDialogFooter>
            <AlertDialogCancel disabled={deletionIsLoading}>
              {t("buttons.cancel")}
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmAddressDeletion}
              disabled={deletionIsLoading}
            >
              {deletionIsLoading ? "Deleting..." : t("buttons.delete")}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
