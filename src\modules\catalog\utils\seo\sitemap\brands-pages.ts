import { MetadataRoute } from "next";
import { retrieveBrandsFromServerSide } from "../../../services/brands/brands-extraction";
import { getBackendLocaleOnServerSide } from "@/utils/backend-locale";

export async function getBrandsPages(): Promise<MetadataRoute.Sitemap> {
  const locale = await getBackendLocaleOnServerSide();

  try {
    const brands = await retrieveBrandsFromServerSide({ locale });

    if (brands) {
      const brandsPages: MetadataRoute.Sitemap = brands.brands.map((brand) => ({
        url: `https://${process.env.FRONTEND_DOMAIN_NAME}/marques/${brand.slug}`,
        lastModified: new Date(),
        changeFrequency: "monthly",
        priority: 1,
      }));

      return [
        //brands page
        {
          url: `https://${process.env.FRONTEND_DOMAIN_NAME}/marques`,
          changeFrequency: "yearly",
          priority: 0.5,
        },
        ...brandsPages,
      ];
    }
  } catch {
    return [];
  }

  return [];
}
