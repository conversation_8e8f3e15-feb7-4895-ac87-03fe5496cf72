"use client";

import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/lib/utils";
import { getCategoryPageUrl } from "@/utils/urls";
import { MoveRight } from "lucide-react";
import Link from "next/link";
import type { HTMLAttributes } from "react";
import { useTranslations } from "use-intl";
import useCategories from "../../hooks/categories/use-categories";

// a container to display the categories list
export default function CategoriesList(props: HTMLAttributes<"div">) {
  const t = useTranslations("modules.catalog.categories.list");
  const { categories, categoriesAreLoading } = useCategories();

  return !categoriesAreLoading && categories ? (
    <section
      className={cn(
        "flex flex-col space-y-8 rounded-[20px] bg-primary px-3 py-2 text-foreground lg:px-6 lg:py-4 xl:px-12 xl:py-7",
        props.className
      )}
    >
      <h1 className="text-2xl font-bold lg:text-3xl">{t("title")}</h1>

      <nav aria-label="categories-navigation">
        <ul className="grid grid-cols-2 gap-8">
          {categories.map((category) => (
            <li key={category.id} className="group">
              <Link
                href={getCategoryPageUrl(category)}
                className="group flex w-full items-center justify-between pr-5 transition-all duration-300 hover:translate-x-1"
              >
                <span className="relative text-lg font-medium transition-all duration-500 before:absolute before:bottom-0 before:left-0 before:h-[1px] before:w-0 before:bg-foreground before:transition-all before:duration-500 before:content-[''] group-hover:before:w-full">
                  {category.name}
                </span>
                <MoveRight className="h-5 w-5 transition-transform duration-300 group-hover:translate-x-1" />
              </Link>
            </li>
          ))}
        </ul>
      </nav>
    </section>
  ) : (
    <div
      className={cn(
        "flex flex-col space-y-8 rounded-[20px] px-3 py-2 text-foreground lg:px-6 lg:py-4 xl:px-12 xl:py-7",
        props.className
      )}
    >
      <Skeleton className="h-10 w-[80%]" />
      <nav aria-label="categories-navigation">
        <ul className="grid grid-cols-2 gap-8">
          {Array.from({ length: 7 }).map((_, idx: number) => (
            <li
              key={idx}
              className="flex items-center justify-between space-x-2 pr-5"
            >
              <Skeleton className="h-8 w-32" />
              <Skeleton className="h-6 w-10" />
            </li>
          ))}
        </ul>
      </nav>
    </div>
  );
}
