# E-commerce Website with Shadcn/UI

A modern, internationalized e-commerce website built with Next.js 15, TypeScript, and Shadcn/UI components. This project features a robust architecture with React Query for data fetching, internationalization support, and a modular component system.

## 🚀 Features

### Core Technologies

- **Next.js 15** with App Router and Turbopack
- **TypeScript** for type safety
- **Tailwind CSS v4** for styling
- **Shadcn/UI** for beautiful, accessible components
- **React Query (TanStack Query)** for server state management

### Internationalization

- **next-intl** for multi-language support
- French as the default locale with locale detection
- Automatic locale prefixing in URLs
- Middleware-based routing for internationalization

### Data Management

- **React Query** with optimized caching (10-minute refetch intervals)
- Custom HTTP client with Axios
- Type-safe API responses with TypeScript interfaces
- Error handling and loading states

### UI Components

- **Carousel** with auto-rotation and keyboard navigation
- **Pagination** with URL state management
- **Toast notifications** with Sonner
- **Button** components with variants
- **Google Analytics** integration

### Development Features

- **ESLint** configuration for code quality
- **PostCSS** with Tailwind CSS
- **Hot reload** with Turbopack
- **TypeScript** strict mode

## 📁 Project Structure

```
src/
├── app/                    # Next.js App Router pages
│   ├── layout.tsx         # Root layout with providers
│   └── page.tsx           # Home page
├── components/
│   └── ui/                # Shadcn/UI components
│       ├── button.tsx
│       ├── carousel.tsx
│       └── sonner.tsx
├── hooks/                 # Custom React hooks
│   ├── use-backend-locale.ts
│   ├── use-carousel-indicator.ts
│   ├── use-landing-page.ts
│   ├── use-pagination.ts
│   └── use-url-params.ts
├── i18n/                  # Internationalization
│   ├── navigation.ts
│   ├── request.ts
│   └── routing.ts
├── lib/                   # Utility libraries
│   ├── http-methods.ts    # HTTP client
│   └── utils.ts
├── middleware.ts          # Next.js middleware
├── services/              # API services
│   └── page-palette/
│       └── landing-page.ts
├── styles/
│   └── globals.css        # Global styles
├── types/                 # TypeScript type definitions
│   └── index.ts
└── utils/                 # Utility functions
    ├── array-in-pairs.ts
    ├── backend-locale.ts
    ├── custom-error.ts
    ├── logger.ts
    ├── react-query-provider.tsx
    ├── sitemap/
    ├── text-transformer.ts
    └── types-casting/
```

## 🛠️ Installation

1. **Clone the repository**

   ```bash
   git clone <repository-url>
   cd ecommerce-website-shadcn
   ```

2. **Install dependencies**

   ```bash
   npm install
   ```

3. **Set up environment variables**
   Create a `.env.local` file with:

   ```env
   BACKEND_ADDRESS=your_backend_api_url
   MEASUREMENT_ID=your_google_analytics_id
   ```

4. **Run the development server**

   ```bash
   npm run dev
   ```

5. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 🚀 Available Scripts

- `npm run dev` - Start development server with Turbopack
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint

## 🌐 Internationalization

The project supports internationalization with the following configuration:

- **Default locale**: French (`fr`)
- **Locale prefix**: Always included in URLs
- **Locale detection**: Automatic based on user preferences

### Adding New Locales

1. Update `src/i18n/routing.ts`:

   ```typescript
   export const routing = defineRouting({
     localeDetection: true,
     locales: ["fr", "en", "es"], // Add new locales
     defaultLocale: "fr",
     localePrefix: "always",
   });
   ```

2. Create translation files in the appropriate directory structure

## 🔧 Custom Hooks

### useLandingPage

Fetches landing page content with React Query caching:

```typescript
const { isLoading, landingPageContent } = useLandingPage();
```

### useCarouselIndicator

Manages carousel state with auto-rotation:

```typescript
const { setApi, current, count } = useCarouselIndicator({
  autoChangement: true,
  timer: 10000,
});
```

### usePagination

Handles pagination with URL state management:

```typescript
const { page, setPage, pagesNumber } = usePagination({
  paginationAffectUrl: true,
  extractPaginationFromUrl: true,
});
```

## 🎨 UI Components

### Carousel

A fully-featured carousel component with:

- Auto-rotation support
- Keyboard navigation
- Touch/swipe support
- Previous/Next buttons
- Accessibility features

### Button

Shadcn/UI button component with multiple variants and sizes.

### Toast Notifications

Integrated with Sonner for beautiful toast notifications.

## 📊 Data Fetching

The project uses React Query for efficient data fetching:

- **Automatic caching** with 10-minute refetch intervals
- **Background updates** on window focus
- **Error handling** with fallback states
- **Loading states** for better UX

### HTTP Client

Custom Axios-based HTTP client with:

- Configurable base URL
- 10-second timeout
- Type-safe request/response handling

## 🔒 Environment Variables

Required environment variables:

- `BACKEND_ADDRESS` - Your backend API URL
- `MEASUREMENT_ID` - Google Analytics measurement ID

## 🚀 Deployment

The project is optimized for deployment on Vercel:

1. Connect your repository to Vercel
2. Set environment variables in Vercel dashboard
3. Deploy automatically on push to main branch

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests and linting
5. Submit a pull request

## 📝 License

This project is licensed under the MIT License.

## 🆘 Support

For support and questions, please open an issue in the repository.
