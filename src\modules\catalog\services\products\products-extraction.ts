import { GET } from "@/lib/http-methods";
import { PaginationType } from "@/types";
import { ProductInResponseType } from "../../types/products";
import { castToProductType } from "../../utils/types-casting/products";
import { CriteriaType } from "../../types";
import { getBackendLocaleOnParams } from "@/utils/backend-locale";

export interface FilterParams {
  page: number;
  limit: number;
  brandSlugs?: string[];
  categoriesSlugs?: string[];
  priceRange?: number[];
  currency?: string;
  availability?: boolean;
  criteria?: CriteriaType;
  search?: string;
  locale?: string;
}

export async function retrieveProductsFromServerSide({
  page,
  limit,
  brandSlugs,
  categoriesSlugs,
  priceRange,
  currency,
  availability = true,
  criteria,
  search,
  locale,
}: FilterParams) {
  const params = [];
  // Conditionally add the query parameters to the params array
  if (brandSlugs && brandSlugs.length > 0) {
    params.push(`brandSlugs=${brandSlugs}`);
  }

  if (categoriesSlugs && categoriesSlugs.length > 0) {
    params.push(`categorySlugs=${categoriesSlugs}`);
  }
  if (priceRange && priceRange.length > 0) {
    params.push(`minPrice=${priceRange[0]}&maxPrice=${priceRange[1]}`);
  }
  if (currency) {
    params.push(`currency=${currency}`);
  }
  params.push(`isStock=${availability}`);
  if (criteria) {
    params.push(`sortBy=${criteria}`);
  }
  if (search) {
    params.push(`search=${search}`);
  }
  if (locale) params.push(getBackendLocaleOnParams({ locale }));

  const filterEndPoint = `/products?page=${page}&limit=${limit}&${params.join(
    "&"
  )}`;

  try {
    const res = await GET(filterEndPoint, {});

    return {
      pagination: res.data.pagination as PaginationType,
      products: (res.data.data as ProductInResponseType[]).map(
        (productInResponse) => castToProductType(productInResponse)
      ),
    };
  } catch (error) {
    return null;
  }
}
