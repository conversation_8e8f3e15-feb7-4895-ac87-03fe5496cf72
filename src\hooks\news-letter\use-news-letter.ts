import { useState } from "react";
import { useTranslations } from "next-intl";
import uploadEmailToServerSide from "@/services/news-letter/email-upload";
import { getNewsLetterFormSchema } from "@/validations/schemas/news-letter";
import { useForm } from "react-hook-form";
import z from "zod";
import { zodResolver } from "@hookform/resolvers/zod";

export default function useNewsLetter() {
  const t = useTranslations("layout.newsLetter.errors");
  const newsLetterValidationContent = useTranslations(
    "layout.newsLetter.validations"
  );
  const formSchema = getNewsLetterFormSchema(newsLetterValidationContent);
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: "",
    },
  });

  const [isPending, setIsPending] = useState(false);
  const [warning, setWarning] = useState("");
  const [isSuccess, setIsSuccess] = useState(false);
  const [isError, setIsError] = useState(false);

  async function onSubmit(values: z.infer<typeof formSchema>) {
    setIsPending(true);
    setIsSuccess(false);
    setIsError(false);
    setWarning("");

    try {
      await uploadEmailToServerSide({ email: values.email });
      setIsSuccess(true);
    } catch (error) {
      setIsSuccess(false);
      setIsError(true);

      setWarning(t("technicalIssue"));
    } finally {
      setIsPending(false);
    }
  }

  function resetState() {
    setIsSuccess(false);
    setIsError(false);
    setWarning("");
  }

  return {
    onSubmit,
    form,
    warning,
    isPending,
    isSuccess,
    isError,
    setIsSuccess,
    resetState,
  };
}
