import Text from "@/styles/text-styles";
import { ProductType } from "../../../../types/products";
import Image from "next/image";
import Link from "next/link";
import useProductUrl from "../../../../hooks/products/use-product-url";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { useEffect, useState } from "react";

interface Props {
  product: ProductType | null;
  onClick?: () => void;
}

export default function SearchProductContainer({ product, onClick }: Props) {
  const productPageUrl = useProductUrl(product);
  const [productImage, setProductImage] = useState(
    "/not-found/product-image.webp"
  );
  const availablePromotion =
    product &&
    product.items[0].prices[0].promotionalPrice !==
      product.items[0].prices[0].realPrice;

  useEffect(() => {
    if (product && product.items.length > 0)
      setProductImage(product.items[0].image);
  }, [product]);

  return product ? (
    <Link
      href={productPageUrl}
      onClick={onClick}
      className="w-full group flex items-center space-x-6 border-primary-light"
    >
      <div className="w-[80px] relative aspect-square">
        <Image
          className="border border-primary rounded-[8px] object-contain"
          fill
          alt="product-image"
          unoptimized
          onError={() => setProductImage("/not-found/product-image.webp")}
          src={productImage}
        />
      </div>
      <div className="flex flex-col">
        <Button
          variant="link"
          className="px-0  max-w-[160px] S:max-w-[200px] M:max-w-[250px] L:max-w-[300px] hover:before:w-full group-hover:before:w-full "
        >
          <Text
            textStyle="TS5"
            className="w-full text-start text-ellipsis overflow-hidden"
          >
            {product.name}
          </Text>
        </Button>
        <p className="text-primary font-bold flex items-center space-x-1 ">
          <Text textStyle="TS6">
            {`${product.items[0].prices[0].currency} ${product.items[0].prices[0].promotionalPrice}`}
          </Text>
          {availablePromotion && (
            <Text textStyle="TS6" className="line-through text-gray">
              {`${product.items[0].prices[0].currency} ${product.items[0].prices[0].realPrice}`}
            </Text>
          )}
        </p>
      </div>
    </Link>
  ) : (
    <div className="group flex space-x-2 py-[15px] border-primary-light">
      <Skeleton className="rounded-[8px] w-[70px] aspect-square" />
      <div className="flex-1 flex flex-col space-y-1">
        <Skeleton className="h-4 w-[150px]" />
        <Skeleton className="h-3 w-[80%]" />
        <div className="flex items-center space-x-1 ">
          <Skeleton className="h-4 w-[60px] aspect-square" />
          <Skeleton className="h-4 w-[60px] aspect-square" />
        </div>
      </div>
    </div>
  );
}
