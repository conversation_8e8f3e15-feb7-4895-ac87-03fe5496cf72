import { GET } from "@/lib/http-methods";
import { CategoryInResponseType } from "../../types/categories";
import { castToCategoryType } from "../../utils/types-casting/categories";
import { getBackendLocaleOnParams } from "@/utils/backend-locale";

interface Params {
  locale?: string;
}

export async function retrieveCategoriesFromServerSide({ locale }: Params) {
  const params = [];

  if (locale) params.push(getBackendLocaleOnParams({ locale }));

  try {
    const res = await GET(`/categories?${params.join("&")}`, {});

    return (res.data as CategoryInResponseType[]).map((categoryInResponse) =>
      castToCategoryType(categoryInResponse)
    );
  } catch (error) {
    return null;
  }
}
