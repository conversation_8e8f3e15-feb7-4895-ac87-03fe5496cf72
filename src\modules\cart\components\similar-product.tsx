import Image from "next/image";
import { ProductItemType } from "../types/products";
import Text from "@/styles/text-styles";
import { useCartStore } from "../store/cart-store";
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { useTranslations } from "next-intl";
import { useEffect, useState } from "react";

interface Props {
  productItem: ProductItemType;
}

export default function SimilarProduct({ productItem }: Props) {
  const t = useTranslations("landingPage.productsOverview");

  const [productImage, setProductImage] = useState(
    "/not-found/product-image.webp"
  );

  const { addProductItem } = useCartStore((store) => store.actions);

  useEffect(() => {
    if (productItem) setProductImage(productItem.image);
  }, [productItem]);

  return (
    <div className="flex">
      <div className="w-full flex gap-4">
        <Image
          src={productImage}
          onError={() => setProductImage("/not-found/product-image.webp")}
          alt={productItem.name}
          width={80}
          height={80}
          unoptimized
          className="h-20 w-20 rounded-lg overflow-hidden border border-secondary object-contain"
        />

        <div className="flex-1 space-y-2">
          <div className="flex justify-between font-medium">
            <h4 className="line-clamp-1">
              <Text textStyle="TS7">{productItem.name}</Text>
            </h4>
          </div>

          <div className="flex-1 flex items-end justify-between space-x-2">
            <p className="text-secondary font-medium">{`${productItem.prices[0].promotionalPrice.toFixed(
              3
            )} ${productItem.prices[0].currency}`}</p>
          </div>
        </div>
      </div>
      <div
        className="flex flex-col items-center justify-center me-2"
        onClick={() =>
          addProductItem({
            slug: productItem.slug,
            id: productItem.id,
            productId: productItem.id,
            cartQuantity: 1,
            name: productItem.name,
            prices: productItem.prices,
            image: productItem.image,
            variations: productItem.variations,
          })
        }
      >
        <Button
          size="tiny"
          variant="ghost"
          className="text-secondary border-[2px] border-secondary rounded-full"
        >
          <Plus className="h-4 w-4" />
        </Button>
        <Text
          textStyle="TS6"
          className="underline text-secondary cursor-pointer"
        >
          {t("acheter")}
        </Text>
      </div>
    </div>
  );
}
