import { getDiscountedPrice } from "@/modules/coupon-codes/utils/coupon-discount-amount";
import { useCartStore } from "../store/cart-store";
import useValidatedCouponCode from "@/modules/coupon-codes/store/coupon-code-validation";

export default function usePrices() {
  const minAmountForFreeShipping = 99;
  const couponCode = useValidatedCouponCode(
    (store) => store.validatedCouponCode
  );
  const { cartItems } = useCartStore((store) => store.state);
  const subtotal = cartItems.reduce(
    (sum, item) => sum + item.prices[0].promotionalPrice * item.cartQuantity,
    0
  );

  let subTotalWithCouponCodeDiscount = 0;

  if (couponCode && couponCode.discount) {
    //couponCode can be for shipping so it'll not have discount
    cartItems.forEach((item) => {
      //item has promotion
      if (item.prices[0].promotionalPrice !== item.prices[0].realPrice) {
        if (couponCode.allowOnPromotions)
          subTotalWithCouponCodeDiscount += getDiscountedPrice(
            couponCode,
            item.prices[0].promotionalPrice * item.cartQuantity
          );
        else
          subTotalWithCouponCodeDiscount +=
            item.prices[0].promotionalPrice * item.cartQuantity;
      } else
        subTotalWithCouponCodeDiscount += getDiscountedPrice(
          couponCode,
          item.prices[0].promotionalPrice * item.cartQuantity
        );
    });
  }

  const couponCodeDiscount: number =
    subTotalWithCouponCodeDiscount !== 0
      ? subtotal - subTotalWithCouponCodeDiscount
      : 0;
  const discountedSubTotal: number = subtotal - couponCodeDiscount;

  const shipping =
    discountedSubTotal >= minAmountForFreeShipping ||
    (couponCode && couponCode.freeShipping)
      ? 0
      : 7;

  const total: number = discountedSubTotal + shipping;

  return {
    total,
    couponCodeDiscount,
    subtotal,
    shipping,
    minAmountForFreeShipping,
  };
}
