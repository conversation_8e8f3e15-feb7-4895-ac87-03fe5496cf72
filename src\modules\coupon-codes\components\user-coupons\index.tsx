import Text from "@/styles/text-styles";
import { useTranslations } from "next-intl";
import UserCouponCodeDetails from "./coupon-details";
import useCoupons from "../../hooks/use-coupons";
import dayjs from "dayjs";
import PaginationMangement from "@/components/pagination/pagination-management";

export default function UserCouponCodes() {
  const t = useTranslations("accountPage.accountInfo");

  const { coupons, couponsAreLoading, setPage, page, pagesNumber } =
    useCoupons(10);

  return (
    !couponsAreLoading && (
      <div>
        <Text textStyle="TS6" className="font-bold text-green">
          {t("myCoupons")}
        </Text>
        {coupons.map((coupon, idx) => (
          <UserCouponCodeDetails
            key={idx}
            code={coupon.code}
            date={dayjs(coupon.period.to)}
            forever={coupon.period.forever}
            discount={coupon.discount}
          />
        ))}
        {pagesNumber > 1 && (
          <div className="flex justify-center mt-12">
            <PaginationMangement
              currentPage={page}
              pagesNumber={pagesNumber}
              changePage={setPage}
            />
          </div>
        )}
      </div>
    )
  );
}
