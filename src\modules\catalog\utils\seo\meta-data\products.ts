import { metadata } from "@/app/[locale]/layout";
import { retrieveProductFromServerSide } from "@/modules/catalog/services/products/product";
import { getBackendLocaleOnServerSide } from "@/utils/backend-locale";
import { AxiosError } from "axios";
import { Metadata } from "next";
import { notFound } from "next/navigation";

interface Params {
  productSlug: string;
}

export async function generateProductMetadata({
  productSlug,
}: Params): Promise<Metadata> {
  try {
    const locale = await getBackendLocaleOnServerSide();
    const product = await retrieveProductFromServerSide({
      slug: productSlug,
      locale,
    });
    if (product?.metaContent) {
      return {
        title: product.metaContent.title,
        description: product.metaContent.description,
        keywords: product.metaContent.keywords,
      };
    }

    return metadata;
  } catch (error) {
    const axiosError = error as AxiosError;

    if (axiosError?.response?.status === 404) notFound();

    return metadata;
  }

  return metadata;
}
