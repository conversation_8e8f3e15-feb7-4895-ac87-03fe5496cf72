import { Checkbox } from "@/components/ui/checkbox";
import { useProductsFilteringStore } from "@/modules/catalog/store/products-filter";
import { CategorySelectionType } from "@/modules/catalog/types/categories";
import { useTranslations } from "next-intl";
import FilterChoiceContainer from "./filter-choice-container";
import Text from "@/styles/text-styles";

export default function CategoriesSelectionDropDown() {
  const t = useTranslations("filtersPage");
  const { joinedPageData, categories, setCategories, applyFilter } =
    useProductsFilteringStore();

  const handleCategoryClick = (choosedCategory: CategorySelectionType) => {
    const selectedCategory = categories.find(
      (category) => category.id === choosedCategory.id
    );

    if (selectedCategory) {
      if (selectedCategory.selected) selectedCategory.selected = false;
      else selectedCategory.selected = true;
    }

    setCategories([...categories]);
    applyFilter();
  };

  return (
    <FilterChoiceContainer title={t.raw("categoriesHeader")}>
      <ul className=" space-y-1">
        {categories.map((category, index) => (
          <li key={index} className="flex items-center space-x-3">
            <Checkbox
              id={category.id}
              className="w-4 h-4"
              checked={category.selected}
              onCheckedChange={() => handleCategoryClick(category)}
            />
            <label
              htmlFor={category.id}
              className="text-sm text-gray cursor-pointer"
            >
              <Text textStyle="TS7">{category.name}</Text>
            </label>
          </li>
        ))}
      </ul>
    </FilterChoiceContainer>
  );
}
