"use client";
import { useProductsFilteringStore } from "@/modules/catalog/store/products-filter";
import { usePathname } from "next/navigation";
import { useEffect } from "react";
import { useTranslations } from "next-intl";
import ProductContainer from "../products/product/container/default";
import PaginationMangement from "@/components/pagination/pagination-management";
import Text from "@/styles/text-styles";
import useProductsFilteringStoreSubmission from "@/modules/catalog/hooks/products/use-products-filtering-store-submission";
import CategoryPageProductsWrapper from "./pages/category-page-products-wrapper";
import FilterPageProductsWrapper from "./pages/filter-page-products-wrapper";
import ExpandableText from "@/components/expandable-text";

interface Props {
  brandSlug?: string;
  categoriesSlugs?: string[];
}

export default function Store({ brandSlug, categoriesSlugs = [] }: Props) {
  const t = useTranslations("filtersPage");
  const pathname = usePathname();

  const { setJoinedPageData, setJoinedPageParam, joinedPageData } =
    useProductsFilteringStore((store) => store);

  const {
    products,
    isLoading,
    page,
    setPage,
    pagesNumber,
    paginatedListRef: scrollViewPortRef,
  } = useProductsFilteringStoreSubmission({
    limit: 20,
    paginationInUrlIsUsed: true,
  });

  useEffect(() => {
    setJoinedPageParam({
      categorySlug: categoriesSlugs[0] ? categoriesSlugs[0] : null,
      subCategorySlug: categoriesSlugs[1] ? categoriesSlugs[1] : null,
      subSubCategorySlug: categoriesSlugs[2] ? categoriesSlugs[2] : null,
      brandSlug: brandSlug ? brandSlug : null,
    });

    return () => {
      setJoinedPageData({
        category: null,
        brand: null,
      });

      setJoinedPageParam({
        categorySlug: null,
        subCategorySlug: null,
        subSubCategorySlug: null,
        brandSlug: null,
      });
    };
  }, [pathname]);

  const FilterWrapper =
    categoriesSlugs.length > 0
      ? CategoryPageProductsWrapper
      : FilterPageProductsWrapper;

  return (
    <>
      <FilterWrapper categorySlug={categoriesSlugs[categoriesSlugs.length - 1]}>
        <div ref={scrollViewPortRef} className="L:px-4 w-full h-full">
          {!isLoading ? (
            products && products.length > 0 ? (
              <div className="w-full grid grid-cols-1 S:grid-cols-2 regularL:grid-cols-3 2extraL:grid-cols-2 XL:grid-cols-4 gap-2 L:gap-5 transition-all">
                {products.map((product, idx) => (
                  <ProductContainer key={idx} product={product} />
                ))}
              </div>
            ) : (
              <div className="min-h-[450px] h-full min-w-full flex items-center justify-center">
                <p className="text-[50px] text-center">
                  <Text textStyle="TS2">{t.raw("NoProducts")}</Text>
                </p>
              </div>
            )
          ) : (
            <div className="w-full grid grid-cols-1 M:grid-cols-2 regularL:grid-cols-3 2extraL:grid-cols-2 XL:grid-cols-4 gap-2 L:gap-5 transition-all">
              {Array.from({ length: 20 }).map((_, idx) => (
                <ProductContainer key={idx} product={null} />
              ))}
            </div>
          )}
        </div>
      </FilterWrapper>
      <div className="flex justify-center mt-4">
        <PaginationMangement
          currentPage={page}
          pagesNumber={pagesNumber}
          changePage={setPage}
        />
      </div>

      {joinedPageData.category && joinedPageData.category.description && (
        <div className="w-full mt-5 bg-primary-muted L:py-4 L:px-5 py-2 px-3 flex flex-col L:space-y-5 space-y-3 line-clamp-3">
          <Text textStyle="TS4" className="text-primary font-bold">
            {t("categoryDetails")}
          </Text>
          <ExpandableText
            text={joinedPageData.category.description as string}
          />
        </div>
      )}
    </>
  );
}
