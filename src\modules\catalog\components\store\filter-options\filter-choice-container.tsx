import {
  Accordion,
  AccordionContent,
  AccordionI<PERSON>,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { ScrollArea } from "@/components/ui/scroll-area";
import useScreenSize from "@/hooks/use-screen-size";
import Text from "@/styles/text-styles";
import Plant from "@assets/icons/plant";
import React from "react";

interface Props {
  title: string;
  children: React.ReactNode[] | React.ReactNode;
  accordian?: boolean;
}

export default function FilterChoiceContainer({
  title,
  children,
  accordian = false,
}: Props) {
  const doubleExtraL = 900;
  const { width } = useScreenSize();

  return width > doubleExtraL ? (
    <div className=" border-y border-primary-light py-5 ">
      <div className="flex flex-col space-y-5">
        <div>
          <h3 className="flex gap-2">
            <Text textStyle="TS6" className="text-primary font-semibold">
              {title}
            </Text>
          </h3>
        </div>
        <ScrollArea className="px-4">
          <div className="h-full max-h-[250px] space-y-1">{children}</div>
        </ScrollArea>
      </div>
    </div>
  ) : (
    <Accordion type="single" collapsible>
      <AccordionItem value="item-1" className="border-b border-primary/40">
        <AccordionTrigger>
          <h3 className="flex gap-2">
            <Plant />
            <Text textStyle="TS6" className="text-primary font-semibold">
              {title}
            </Text>
          </h3>
        </AccordionTrigger>
        <AccordionContent className="px-4">{children}</AccordionContent>
      </AccordionItem>
    </Accordion>
  );
}
