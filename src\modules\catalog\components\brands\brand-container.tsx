import { buttonVariants } from "@/components/ui/button";
import { BrandType } from "../../types/brands";
import { useEffect, useState } from "react";
import Link from "next/link";
import { getBrandPageUrl } from "@/utils/urls";
import { cn } from "@/lib/utils";

interface Props {
  brand: BrandType;
}

export default function BrandContainer({ brand }: Props) {
  const [brandImage, setBrandImage] = useState("/not-found/product-image.webp");
  useEffect(() => {
    if (brand) setBrandImage(brand.image);
  }, [brand]);

  return (
    <Link
      href={getBrandPageUrl(brand.slug)}
      className={cn(buttonVariants({ variant: "ghost" }), "h-fit")}
    >
      <img
        src={brandImage}
        alt={brand.name}
        className="h-20 object-contain group-hover:scale-110 duration-200"
      />
    </Link>
  );
}
