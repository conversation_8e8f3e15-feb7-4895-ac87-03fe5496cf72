"use client";

import { But<PERSON>, buttonVariants } from "@/components/ui/button";
import Logo from "../logo";
import { NavMenu } from "./nav-menu";
import { NavigationSheet } from "./navigation-sheet";
import { ShoppingBag, SunIcon, User } from "lucide-react";
import Link from "next/link";
import { cn } from "@/lib/utils";
import { STATIC_URLS } from "@/utils/urls";
import useUser from "@/modules/auth/hooks/account-management/use-user";

const Navbar = () => {
  const {} = useUser();
  return (
    <nav className="h-16 bg-background border-b">
      <div className="h-full flex items-center justify-between max-w-screen-2xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center gap-8">
          <Logo />

          {/* Desktop Menu */}
          <NavMenu className="hidden md:block" />
        </div>

        <div className="flex items-center gap-3">
          <Link
            href={STATIC_URLS["auth"].signIn}
            className={cn(buttonVariants({ variant: "outline" }))}
          >
            <User />
          </Link>
          <Link
            href={STATIC_URLS["auth"].signIn}
            className={cn(buttonVariants({ variant: "outline" }))}
          >
            <ShoppingBag />
          </Link>
          <Button size="icon" variant="outline">
            <SunIcon />
          </Button>

          {/* Mobile Menu */}
          <div className="md:hidden">
            <NavigationSheet />
          </div>
        </div>
      </div>
    </nav>
  );
};

export default Navbar;
