import { GET } from "@/lib/http-methods";
import { AxiosError } from "axios";
import { OrderDataType, OrderResponseDataType } from "../../types/orders";
import { PaginationType } from "@/types";
import { castToOrderType } from "../../utils/types-casting/order";
import { refreshToken } from "@/modules/auth/services/refresh-token";
import extractJWTokens from "@/modules/auth/utils/jwt/extract-tokens";
import { getBackendLocaleOnParams } from "@/utils/backend-locale";

interface Params {
  page: number;
  limit: number;
  locale?: string;
}

export async function retrieveUserOrders({
  page,
  limit,
  locale,
}: Params): Promise<{
  orders: OrderDataType[];
  pagination: PaginationType;
} | null> {
  const { access } = extractJWTokens();
  const header = {
    Authorization: `Bearer ${access}`,
  };

  const params = [`page=${page}`, `limit=${limit}`];

  if (locale) params.push(getBackendLocaleOnParams({ locale }));

  try {
    const res = await GET(
      `${process.env.BACKEND_ADDRESS}/orders?${params.join("&")}`,
      header
    );
    return {
      pagination: res.data.pagination as PaginationType,
      orders: (res.data.data as OrderResponseDataType[]).map(
        (orderInResponse) => castToOrderType(orderInResponse)
      ),
    };
  } catch (error) {
    const axiosError = error as AxiosError;
    if (axiosError.response?.status === 401) {
      const res = await refreshToken(() =>
        retrieveUserOrders({ page, limit, locale })
      );

      //unauthorized user error is already handled by the user hook
      if (!res)
        return {
          orders: [],
          pagination: {
            records: 1,
            currentPage: 1,
            totalPages: 1,
          },
        };
      return res;
    }

    return {
      orders: [],
      pagination: {
        records: 1,
        currentPage: 1,
        totalPages: 1,
      },
    };
  }
}
