//this component is used only to make client side rendering
"use client";
import { Button } from "@/components/ui/button";
import { ProductType } from "@/modules/catalog/types/products";
import { ShoppingCartIcon } from "lucide-react";
import { useTranslations } from "next-intl";

interface Props {
  product: ProductType;
}

export default function CartActionButton({ product }: Props) {
  // const { addProductItem } = useCartStore((store) => store.actions);
  const t = useTranslations("shared.cart");

  return (
    <Button
      className="bg-primary text-primary-foreground hover:bg-primary/90 rounded-md px-4 py-2 text-sm font-medium transition-colors"
      // onClick={() => {
      //   addProductItem({
      //     slug: product.slug,
      //     id: product.items[0].id,
      //     productId: product.id,
      //     cartQuantity: 1,
      //     name: product.name,
      //     prices: product.items[0].prices,
      //     image: product.items[0].image,
      //     variations: product.items[0].variations,
      //   });
      // }}
    >
      <ShoppingCartIcon />
      {t("addToCart")}
    </Button>
  );
}
