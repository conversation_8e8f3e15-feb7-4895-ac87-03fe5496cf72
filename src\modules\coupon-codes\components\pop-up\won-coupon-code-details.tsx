import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import TimeDownCounter from "@/components/time-down-counter";
import useCurrency from "@/modules/catalog/hooks/use-currency";
import Text, { TextStyle } from "@/styles/text-styles";
import { Dayjs } from "dayjs";
import { useTranslations } from "next-intl";
import { useRouter } from "next/navigation";

interface Props {
  amountOrPercentage?: number;
  code: string;
  date?: Dayjs;
  forever: boolean;
  type: string;
}

export default function WonCouponCodeDetails({
  forever,
  type,
  amountOrPercentage,
  code,
  date,
}: Props) {
  const t = useTranslations("shared.coupon");
  const router = useRouter();

  const { currency } = useCurrency();

  return (
    <div className="flex flex-col items-center bg-white p-6 rounded-lg space-y-6">
      <div className="flex items-center space-x-2 mb-6">
        <Text textStyle="TS2" className="font-bold text-black">
          {t("title")}
        </Text>
      </div>

      <div className="flex items-center space-x-2 mb-6">
        <Text textStyle="TS7" className="text-center">
          {t.rich("description", {
            amount: () => (
              <span className="font-semibold">
                {type == "percentage"
                  ? `${amountOrPercentage} %`
                  : `${amountOrPercentage} ${currency}`}
              </span>
            ),
          })}
        </Text>
      </div>

      <div className="border-2 border-dashed border-green rounded-lg p-4 mb-6 w-full text-center">
        <Text textStyle="TS4" className="text-center font-mono text-green">
          {code}
        </Text>
      </div>

      {/* Timer */}
      {date && forever == false && (
        <Text textStyle="TS7" className="text-center text-green mb-6 underline">
          <TimeDownCounter
            className={cn("underline", TextStyle["TS6"])}
            targetDate={date}
          />
        </Text>
      )}

      <Button
        className="bg-green text-white h-12 w-full rounded-xl group "
        onClick={() => router.push("/produits/filtres")}
      >
        <Text textStyle="TS6" className="text-white group-hover:text-green">
          {t.raw("passerBoutique")}
        </Text>
      </Button>
    </div>
  );
}
