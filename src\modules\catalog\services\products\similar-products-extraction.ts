import { GET } from "@/lib/http-methods";
import { PaginationType } from "@/types";
import { ProductInResponseType } from "../../types/products";
import { castToProductType } from "../../utils/types-casting/products";
import { getBackendLocaleOnParams } from "@/utils/backend-locale";

interface Params {
  page: number;
  limit: number;
  slug: string;
  locale?: string;
}

export async function retrieveSimilarProductsFromServerSide({
  page,
  limit,
  slug,
  locale,
}: Params) {
  const params = [];

  if (locale) params.push(getBackendLocaleOnParams({ locale }));

  try {
    const res = await GET(
      `/products/similar/${slug}?page=${page}&limit=${limit}&${params.join(
        "&"
      )}`,
      {}
    );

    return {
      pagination: res.data.pagination as PaginationType,
      products: (res.data.data as ProductInResponseType[]).map(
        (productInResponse) => castToProductType(productInResponse)
      ),
    };
  } catch (error) {
    return null;
  }
}
