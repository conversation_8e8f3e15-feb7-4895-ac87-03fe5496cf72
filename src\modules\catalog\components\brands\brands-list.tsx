"use client";
import PaginationManagement from "@/components/pagination/pagination-management";
import { Skeleton } from "@/components/ui/skeleton";
import BrandContainer from "@/modules/catalog/components/brands/brand-container";
import useBrands from "@/modules/catalog/hooks/brands/use-brands";
import { useTranslations } from "next-intl";

export default function BrandsList() {
  const { brands, brandsAreLoading, page, pagesNumber, setPage } = useBrands({
    limit: 30,
    paginationAffectUrl: true,
  });

  const t = useTranslations("modules.catalog.brands.list");

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-semibold text-foreground">
          {t.raw("title")}
        </h1>
      </div>

      {/* Brands Grid */}
      <div className="grid grid-cols-2 gap-4 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6">
        {brandsAreLoading ? (
          <>
            {Array.from({ length: 30 }).map((_, idx) => (
              <Skeleton key={idx} className="h-[100px] w-full rounded-lg" />
            ))}
          </>
        ) : (
          brands?.map((brand, idx) => (
            <BrandContainer key={brand.id || idx} brand={brand} />
          ))
        )}
      </div>
      <div className="flex justify-center mt-4">
        <PaginationManagement
          currentPage={page}
          totalPages={pagesNumber}
          onPageChange={setPage}
          showPreviousNext={false}
        />
      </div>
    </div>
  );
}
