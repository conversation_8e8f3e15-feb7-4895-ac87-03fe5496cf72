"use client";
import { useTranslations } from "next-intl";
import { cn } from "@/lib/utils";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Separator } from "@/components/ui/separator";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import PaymentOptionsSelection from "./payment-options-seletion";
import AddressesSelection from "./addresses/addresses-selection";
import AddressCreationForm from "./addresses/form/address-creation-form";
import useUserStore from "@/modules/auth/store/user-store";
import useAddressSelection from "../store/address-selection-store";
import { useAuthDialogState } from "@/modules/auth/store/auth-dialog-state-store";
import useCheckoutSubmission from "../hooks/use-checkout-submission";

export default function CheckoutForm() {
  const t = useTranslations("modules.checkout.checkout");
  const { user, isLoading: userIsLoading } = useUserStore((store) => store);
  const selectedAddressId = useAddressSelection(
    (store) => store.selectedAddressId
  );
  const setAuthDialogIsOpen = useAuthDialogState((store) => store.setIsOpen);

  const { form, error, onSubmit, isPending } = useCheckoutSubmission({
    user,
    selectedAddressId,
  });

  return (
    <div className="w-full flex flex-col space-y-6 text-primary p-5">
      {!userIsLoading ? (
        (!user || (user && !user.isAuthenticated)) && (
          <div className="w-full px-4 rounded-2xl bg-primary-muted flex justify-center py-5 text-center text-black">
            {t.rich("loginToFasterPayment", {
              link: (chunk) => (
                <Button
                  variant="ghost"
                  className="px-0 underline underline-offset-[5px] decoration-[1.5px]"
                  onClick={() => setAuthDialogIsOpen(true)}
                >
                  {chunk}
                </Button>
              ),
            })}
          </div>
        )
      ) : (
        <div className="w-full px-4 rounded-2xl bg-primary-muted flex flex-col items-center space-y-2 py-5 text-center text-black">
          {Array.from({ length: 2 }).map((_, idx) => (
            <Skeleton
              key={idx}
              className={cn("h-4 w-[80%] min-w-[150px] bg-gray bg-opacity-50", {
                "w-[calc(80%-100px)] min-w-[130px]": idx === 1,
              })}
            />
          ))}
        </div>
      )}

      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="flex flex-col space-y-6"
        >
          {/* Address Section */}
          <section className="flex flex-col space-y-4">
            <h3>Adresse de livraison</h3>
            <AddressesSelection />
            {selectedAddressId === "" && (
              <AddressCreationForm useAddressIdInCheckout={true} />
            )}
          </section>

          <Separator />

          <section className="flex flex-col space-y-4">
            <h3>{t("fields.deliveryNote.label")}</h3>
            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="sr-only">
                    {t("fields.deliveryNote.label")}
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      className="L:min-h-[128px] border border-primary"
                      placeholder={t("fields.deliveryNote.placeholder")}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </section>

          <Separator />

          {/* Payment Options Section */}
          <section className="flex flex-col space-y-4">
            <h3>{t("fields.payment.label")}</h3>
            <FormField
              control={form.control}
              name="paymentMethod"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="sr-only">
                    {t("fields.payment.label")}
                  </FormLabel>
                  <FormControl>
                    <PaymentOptionsSelection
                      value={field.value}
                      onChange={field.onChange}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </section>

          <Separator />

          {/* Error Display */}
          {error && <p className="text-danger text-center">{error}</p>}

          {/* Submit Button */}
          <Button
            type="submit"
            className={cn("w-full h-[50px] rounded-xl", {
              "scale-95": isPending,
            })}
            disabled={isPending}
          >
            {isPending ? "Traitement en cours..." : t("actions.confirm")}
          </Button>
        </form>
      </Form>
    </div>
  );
}
