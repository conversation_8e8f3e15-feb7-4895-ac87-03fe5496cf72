import { Dispatch, SetStateAction } from "react";
import { useProductsFilteringStore } from "@/modules/catalog/store/products-filter";
import SidebarSkeletons from "./sidebar-skeletons";
import CategoriesSelectionDropDown from "./categories-selection";
import BrandsSelectionDropDown from "./brands-selection-dropdown";
import PriceRangeDropdown from "./price-range-dropdown";
import Text from "@/styles/text-styles";
import { useTranslations } from "next-intl";
import { Separator } from "@/components/ui/separator";
import { Button } from "@/components/ui/button";
import { X } from "lucide-react";
import SoritingCriteriaDropDown from "./sorting-criteria-dropdown";
import SubCategoriesSelectionDropDown from "./subcategories-selection";
import useScreenSize from "@/hooks/use-screen-size";
import { ScrollArea } from "@/components/ui/scroll-area";

interface Props {
  setFilterIsOpen: Dispatch<SetStateAction<boolean>>;
  isLoading: boolean;
  filterHeaderIsUsed?: boolean;
  criteriaDropDownIsUsed?: boolean;
}

export default function FilterOptions({
  setFilterIsOpen,
  isLoading = false,
  filterHeaderIsUsed = false,
  criteriaDropDownIsUsed = true,
}: Props) {
  const t = useTranslations("filtersPage");
  const doubleExtraL = 900;
  const { width } = useScreenSize();

  const { joinedPageParam, categories } = useProductsFilteringStore();
  const subCategories = categories.flatMap((category) =>
    category.selected ? category.subCategories : []
  );

  return (
    <aside className="w-full h-full flex flex-col">
      {/* Header Section */}
      {filterHeaderIsUsed && (
        <>
          <div className="px-4 pt-4 w-full flex justify-between">
            <h2>
              <Text textStyle="TS2">{t("filterHeader")}</Text>
            </h2>
            <Button variant="ghost" onClick={() => setFilterIsOpen(false)}>
              <X size={25} />
            </Button>
          </div>

          <Separator />
        </>
      )}

      {/* Content Section */}
      {!isLoading ? (
        width > doubleExtraL ? (
          <div className="flex-grow">
            <div className="2extraL:p-0 p-6">
              {criteriaDropDownIsUsed && <SoritingCriteriaDropDown />}

              {/* Categories */}
              {categories.length > 0 && <CategoriesSelectionDropDown />}

              {/* Categories */}
              {subCategories.length > 0 && <SubCategoriesSelectionDropDown />}

              {/* Price Range */}
              <PriceRangeDropdown />

              {/* Brands */}
              {joinedPageParam.brandSlug &&
              joinedPageParam.brandSlug !== "" ? null : (
                <BrandsSelectionDropDown />
              )}
            </div>
          </div>
        ) : (
          <ScrollArea className="flex-grow">
            <div className="max-h-[calc(80vh-200px)] 2extraL:p-0 p-6">
              {criteriaDropDownIsUsed && <SoritingCriteriaDropDown />}

              {/* Price Range */}
              <PriceRangeDropdown />

              {/* types */}
              {<CategoriesSelectionDropDown />}

              {/* Categories */}
              {<SubCategoriesSelectionDropDown />}

              {/* Brands */}
              {joinedPageParam.brandSlug &&
              joinedPageParam.brandSlug !== "" ? null : (
                <BrandsSelectionDropDown />
              )}
            </div>
          </ScrollArea>
        )
      ) : (
        <SidebarSkeletons />
      )}

      {/* Sticky Footer Section */}
      <div className="2extraL:hidden pt-10 flex justify-center 2extraL:px-0 px-4">
        <Button
          className="w-full py-6 border border-primary"
          onClick={() => {
            setFilterIsOpen(false);
          }}
        >
          <Text textStyle="TS6">{t.raw("apply")}</Text>
        </Button>
      </div>
    </aside>
  );
}
