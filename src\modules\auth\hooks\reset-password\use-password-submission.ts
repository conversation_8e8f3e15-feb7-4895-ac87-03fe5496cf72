import { useState } from "react";

import { useTranslations } from "next-intl";
import { useForm } from "react-hook-form";
import z from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { CustomError } from "@/utils/custom-error";
import { getPasswordSchema } from "../../validation/schemas/reset-password/password";
import { resetPassword } from "../../services/reset-password/password-submission";

interface Params {
  onSuccess: () => void;
  code: string;
  email: string;
}

export default function usePasswordSubmission({
  code,
  email,
  onSuccess,
}: Params) {
  const t = useTranslations("modules.auth.validations");
  const errorsContent = useTranslations("modules.auth.errors");

  const [isPending, setIsPending] = useState(false);
  const [error, setError] = useState("");

  const formSchema = getPasswordSchema(t);
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      password: "",
    },
  });

  async function onSubmit(values: z.infer<typeof formSchema>) {
    if (error !== "") setError("");

    try {
      await resetPassword({
        email,
        code,
        password: values.password,
      });

      onSuccess();
    } catch (e) {
      const error = e as CustomError;

      if (error.status === 404) {
        setError(errorsContent("notFound"));
      } else {
        setError(errorsContent("technicalIssue"));
      }
    } finally {
      setIsPending(false);
    }
  }

  return { error, onSubmit, isPending, form };
}
