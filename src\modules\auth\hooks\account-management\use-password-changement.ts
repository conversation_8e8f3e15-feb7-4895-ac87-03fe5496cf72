import { useForm } from "react-hook-form";
import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useState } from "react";
import { useRouter } from "next/navigation";
import { useTranslations } from "next-intl";

import { changePassword } from "@/modules/auth/services/password-changement";
import { CustomError } from "@/utils/custom-error";
import z from "zod";
import { getPasswordChangementSchema } from "../../validation/schemas/password-changement";

export default function usePasswordChangement() {
  const router = useRouter();
  const t = useTranslations("modules.auth.validations");
  const errorsContent = useTranslations("modules.auth.errors");

  const [isPending, setIsPending] = useState(false);
  const [passwordChanged, setPasswordChanged] = useState(false);
  const [error, setError] = useState("");

  const passwordChangementSchema = getPasswordChangementSchema(t);
  const form = useForm<z.infer<typeof passwordChangementSchema>>({
    resolver: zod<PERSON><PERSON><PERSON>ver(passwordChangementSchema),
    defaultValues: {
      currentPassword: "",
      newPassword: "",
      confirmationPassword: "",
    },
  });

  async function onSubmit(values: z.infer<typeof passwordChangementSchema>) {
    if (error !== "") setError("");
    setIsPending(true);

    try {
      const response = await changePassword({
        oldPassword: values.currentPassword,
        newPassword: values.newPassword,
      });

      form.reset();
      setPasswordChanged(true);

      setTimeout(() => {
        setPasswordChanged(false);
      }, 4000);
    } catch (e) {
      const error = e as CustomError;

      if (error.status === 400) {
        if (error.code === "P2011")
          setError(errorsContent("featureNotAvailableForFacebookOrGoogle"));
        else setError(errorsContent("invalidData"));
      } else if (error.status === 404) {
        setError(errorsContent("notFound"));
      } else {
        setError(errorsContent("technicalIssue"));
      }
    } finally {
      setIsPending(false);
    }
  }

  return {
    isPending,
    onSubmit,
    form,
    error,
    passwordChanged,
  };
}
