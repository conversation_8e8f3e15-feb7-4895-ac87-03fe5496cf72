import { retrieveCities } from "@/modules/checkout/services/addresses/cities-extraction";
import { useQuery } from "@tanstack/react-query";
import { useEffect } from "react";
import { useCheckoutStore } from "../../store/checkout-store";
import useBackendLocale from "@/hooks/use-backend-locale";

interface Params {
  countryCode: string;
}

export default function useCountryCities({ countryCode }: Params) {
  const { backendLocale } = useBackendLocale();
  const { city, setCity } = useCheckoutStore((store) => store);
  const { isLoading, data } = useQuery({
    queryKey: [countryCode, backendLocale],
    queryFn: () => retrieveCities({ countryCode, locale: backendLocale }),
    initialData: [],
    enabled: countryCode !== undefined && countryCode !== "",
  });

  useEffect(() => {
    if (data.length > 0) setCity(data[0]);
  }, [data.length]);

  function changeCity(cityCode: string) {
    const selectedCity = data.find(
      (searchedCity) => searchedCity.code === cityCode
    );

    if (selectedCity) setCity(selectedCity);
  }

  return {
    cities: data,
    citiesAreLoading: isLoading,
    city,
    changeCity,
  };
}
