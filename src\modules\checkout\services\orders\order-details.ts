import { GET } from "@/lib/http-methods";
import { castToOrderType } from "../../utils/types-casting/order";
import { OrderDataType, OrderResponseDataType } from "../../types/orders";
import extractJWTokens from "@/modules/auth/utils/jwt/extract-tokens";
import { AxiosError } from "axios";
import { refreshToken } from "@/modules/auth/services/refresh-token";
import { CustomError } from "../../../../utils/custom-error";
import { getBackendLocaleOnParams } from "@/utils/backend-locale";

interface Params {
  orderId: string;
  locale?: string;
}

export async function retrieveGuestUserOrderDetails({
  locale,
  orderId,
}: Params): Promise<OrderDataType | null> {
  const params = [];

  if (locale) params.push(getBackendLocaleOnParams({ locale }));

  try {
    const res = await GET(
      `${process.env.BACKEND_ADDRESS}/orders/guest/${orderId}?${params.join(
        "&"
      )}`,
      {}
    );
    return castToOrderType(res.data as OrderResponseDataType);
  } catch (error) {
    return null;
  }
}

export async function retrieveUserOrderDetails({
  orderId,
  locale,
}: Params): Promise<OrderDataType | null> {
  const { access } = extractJWTokens();
  const headers = {
    Authorization: `Bearer ${access}`,
  };

  const params = [];

  if (locale) params.push(getBackendLocaleOnParams({ locale }));

  try {
    const res = await GET(
      `${
        process.env.BACKEND_ADDRESS
      }/orders/authenticated/${orderId}?${params.join("&")}`,
      headers
    );
    return castToOrderType(res.data as OrderResponseDataType);
  } catch (error) {
    const axiosError = error as AxiosError;

    if (axiosError.response?.status === 401) {
      const res = await refreshToken(() =>
        retrieveUserOrderDetails({ orderId, locale })
      );

      //unauthorized user error is already handled by the user hook
      if (!res) throw new CustomError("Unauthorized!", 401);

      return res;
    }

    return null;
  }
}
