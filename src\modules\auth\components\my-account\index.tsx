"use client";

import type React from "react";
import { useWindowWidth } from "@react-hook/window-size";
import { cn } from "@/lib/utils";
import { notFound, usePathname } from "next/navigation";
import Link from "next/link";
import { ChevronLeft, User, Package, MapPin, Settings } from "lucide-react";
import { useTranslations } from "next-intl";
import useUserStore from "@/modules/auth/store/user-store";
import AccountMenu from "@/modules/auth/components/my-account/account-menu"; // Declare the AccountMenu variable

interface AccountPageProps {
  children: React.ReactNode;
}

export default function AccountPage({ children }: AccountPageProps) {
  const width = useWindowWidth();
  const { user, isLoading } = useUserStore((store) => store);
  const t = useTranslations("accountPage");
  const menuContent = useTranslations("accountPage.menuList");
  const pathname = usePathname();

  const menuButtons = [
    {
      icon: <User className="h-5 w-5" />,
      pathname: "/mon-compte/info",
      name: "myAccount",
    },
    {
      icon: <Package className="h-5 w-5" />,
      pathname: "/mon-compte/commandes",
      name: "ordersHistory",
    },
    {
      icon: <MapPin className="h-5 w-5" />,
      pathname: "/mon-compte/adresses",
      name: "adress",
    },
    {
      icon: <Settings className="h-5 w-5" />,
      pathname: "/mon-compte/parametres",
      name: "settings",
    },
  ];

  // Find the active button based on the current pathname starting with the button's pathname
  const choosedButton = menuButtons.find((btn) =>
    pathname.startsWith(btn.pathname)
  );

  // Authentication check
  if (!isLoading && !user?.isAuthenticated) notFound();

  return (
    <div className="md:pt-[10px] max-w-screen-2xl min-h-[70vh] flex flex-col space-y-3 mt-12 w-full">
      <div className="flex items-center space-x-4 mb-8">
        {choosedButton && (
          <Link
            href={width < 850 ? "/mon-compte" : "/"}
            className="flex items-center gap-2 text-primary font-semibold"
          >
            <ChevronLeft className="h-4 w-4 text-primary" />
            <span className="text-lg font-semibold">
              {width < 850 ? menuContent(choosedButton.name) : t("home")}
            </span>
          </Link>
        )}
      </div>
      <div
        className={cn(
          "relative flex lg:flex-row flex-col lg:space-x-10 lg:space-y-0 space-y-10 space-y-reverse "
        )}
      >
        {/* Conditional rendering for the AccountMenu based on screen size and path */}
        {(width < 850 && pathname === "/mon-compte") || width >= 850 ? (
          <div
            className={cn("lg:flex-none flex-1", {
              "order-2": pathname === "/mon-compte" && width < 850,
            })}
          >
            <AccountMenu menuButtons={menuButtons} />
          </div>
        ) : null}
        <div
          className={cn(
            "flex-1 xl:px-5 lg:px-2 px-4 lg:pl-[30px] xl:pl-[60px]",
            {
              "order-1": pathname === "/mon-compte" && width < 850,
            }
          )}
        >
          {children}
        </div>
      </div>
    </div>
  );
}
