import { retrieveProductsFromServerSide } from "@/modules/catalog/services/products/products-extraction";
import { getBackendLocaleOnServerSide } from "@/utils/backend-locale";
import { MetadataRoute } from "next";

export async function getProductsPages(): Promise<MetadataRoute.Sitemap> {
  try {
    const locale = await getBackendLocaleOnServerSide();
    const data = await retrieveProductsFromServerSide({
      page: 1,
      limit: 500,
      locale,
    });

    if (data) {
      const pagination = data.pagination;
      const productsPages: MetadataRoute.Sitemap = data.products.flatMap(
        (product) => [
          {
            url: `https://${process.env.FRONTEND_DOMAIN_NAME}/produits/${product.slug}`,
            lastModified: new Date(),
            changeFrequency: "monthly",
            priority: 1,
          },
          {
            url: `https://${process.env.FRONTEND_DOMAIN_NAME}/produits/similaires/${product.slug}`,
            lastModified: new Date(),
            changeFrequency: "monthly",
            priority: 1,
          },
        ]
      );

      for (let page = 2; page <= pagination.totalPages; page++) {
        const data = await retrieveProductsFromServerSide({
          page,
          limit: 500,
          locale,
        });
        if (data) {
          const currentPageProductsPages: MetadataRoute.Sitemap =
            data.products.flatMap((product) => [
              {
                url: `https://${process.env.FRONTEND_DOMAIN_NAME}/produits/${product.slug}`,
                lastModified: new Date(),
                changeFrequency: "monthly",
                priority: 1,
              },
              {
                url: `https://${process.env.FRONTEND_DOMAIN_NAME}/produits/similaires/${product.slug}`,
                lastModified: new Date(),
                changeFrequency: "monthly",
                priority: 1,
              },
            ]);
          productsPages.push(...currentPageProductsPages);
        }
      }

      return productsPages;
    }
  } catch {
    return [];
  }

  return [];
}
