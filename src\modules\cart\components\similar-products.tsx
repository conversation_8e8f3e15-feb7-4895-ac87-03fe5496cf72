"use client";
import Text from "@/styles/text-styles";
import { useTranslations } from "use-intl";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CustomCarouselNext,
  CustomCarouselPrevious,
} from "@/components/ui/carousel";
import SimilarProduct from "./similar-product";
import { useCartStore } from "../store/cart-store";
import useCartSimilarProducts from "../hooks/use-similar-products";

export default function SimilarProducts() {
  const sharedContent = useTranslations("shared.sections");

  const [cartItem] = useCartStore((store) => store.state.cartItems);
  const { products, productsAreLoading } = useCartSimilarProducts({
    limit: 8,
    similarProductSlug: cartItem ? cartItem.slug : "",
  });

  return products && !productsAreLoading ? (
    products.length > 0 ? (
      <div className="max-w-full bg-secondary-light">
        <Carousel
          opts={{ loop: true }}
          className="w-full flex flex-col pt-2 pb-4 pr-4 L:px-6 px-4"
        >
          <div className="flex justify-between items-center">
            <div className="relative">
              <Text textStyle="TS5" className="text-black font-bold">
                {sharedContent("youMayLike")}
              </Text>
              <span className="absolute  translate-x-1  w-2 h-2 bg-secondary rounded-full"></span>
            </div>
            <div className="flex items-center space-x-2">
              <CustomCarouselPrevious className="p-1 w-6 h-6 border" />
              <CustomCarouselNext className="p-1 w-6 h-6 border" />
            </div>
          </div>

          <CarouselContent className="flex space-x-2">
            {products.map((product) =>
              product.items.map((productItem) => (
                <CarouselItem key={productItem.id} className="w-full">
                  <SimilarProduct
                    key={product.id}
                    productItem={{
                      slug: product.slug,
                      id: productItem.id,
                      productId: product.id,
                      cartQuantity: 1,
                      name: product.name,
                      prices: productItem.prices,
                      image: productItem.image,
                      variations: productItem.variations,
                    }}
                  />
                </CarouselItem>
              ))
            )}
          </CarouselContent>
        </Carousel>
      </div>
    ) : null
  ) : null;
}
