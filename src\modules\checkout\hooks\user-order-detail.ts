import { useQuery } from "@tanstack/react-query";
import {
  retrieveGuestUserOrderDetails,
  retrieveUserOrderDetails,
} from "../services/orders/order-details";
import { OrderDataType } from "../types/orders";
import { CustomError } from "../../../utils/custom-error";
import { useAuthDialogState } from "@/modules/auth/store/auth-dialog-state-store";
import useUserStore from "@/modules/auth/store/user-store";
import useBackendLocale from "@/hooks/use-backend-locale";

export default function useOrderDetail(orderId: string) {
  const { backendLocale } = useBackendLocale();

  const { user, isLoading: userIsLoading } = useUserStore((store) => store);
  const { setIsOpen: setAuthPopUpIsOpen } = useAuthDialogState();
  const { data, isLoading, error } = useQuery<
    OrderDataType | null,
    CustomError
  >({
    queryKey: [orderId, user, userIsLoading, backendLocale],
    queryFn: () =>
      user !== null && user.isAuthenticated
        ? retrieveUserOrderDetails({ orderId, locale: backendLocale })
        : retrieveGuestUserOrderDetails({ orderId, locale: backendLocale }),
  });

  if (error && error.status === 401) {
    setAuthPopUpIsOpen(true);
  }

  return {
    order: data,
    isLoading,
  };
}
