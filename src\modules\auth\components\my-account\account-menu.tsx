"use client";

import { usePathname, useRouter } from "next/navigation";
import { Skeleton } from "@/components/ui/skeleton";
import useAddresses from "@/modules/checkout/hooks/addresses/use-addresses";
import { useTranslations } from "next-intl";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import useUserStore from "@/modules/auth/store/user-store";
import type React from "react";
import { logout } from "../../utils/logout";

interface Props {
  menuButtons: { name: string; icon: React.ReactNode; pathname: string }[];
}

export default function AccountMenu({ menuButtons }: Props) {
  const pathname = usePathname();
  const router = useRouter();
  const { user, isLoading } = useUserStore((store) => store);
  const { addressesAreLoading } = useAddresses();
  const t = useTranslations("accountPage.menuList");

  // Determine if loading state should show skeleton
  const showSkeleton = isLoading || addressesAreLoading;

  return showSkeleton ? (
    <div className="px-2 lg:w-fit w-full flex flex-col">
      <div className={"lg:flex hidden px-2 py-3 space-x-[10px]"}>
        <Skeleton className="h-11 w-11 rounded-full" />
        <Skeleton className="h-11 w-52" />
      </div>
      <div className="flex flex-col space-y-1">
        {Array.from({ length: 3 }).map((_, idx) => (
          <Skeleton key={idx} className="h-16 w-full" />
        ))}
      </div>
      <Skeleton className="mt-12 h-14 w-full" />
    </div>
  ) : (
    <div className="lg:w-fit w-full flex flex-col">
      <div className="flex flex-col space-y-5">
        {menuButtons.map((btn, idx) => (
          <Button
            key={idx}
            variant="outline" // Use outline variant for default look
            className={cn(
              "w-full px-5 py-6 md:px-5 md:py-8 flex justify-between rounded-2xl border border-input", // Default border
              "hover:bg-accent hover:text-accent-foreground", // Standard shadcn hover
              "data-[state=active]:bg-primary data-[state=active]:text-primary-foreground", // Active state
              {
                "bg-primary text-primary-foreground": pathname === btn.pathname, // Override for active if needed
              }
            )}
            data-state={pathname === btn.pathname ? "active" : "inactive"} // Custom data attribute for active state
            onClick={() => router.push(btn.pathname)}
          >
            <div className="flex items-center space-x-[10px]">
              {btn.icon}
              <span className="text-base font-medium whitespace-nowrap">
                {t(btn.name)}
              </span>
            </div>
          </Button>
        ))}
      </div>
      <Button
        className={cn(
          "mt-12 lg:w-full w-fit flex lg:mx-0 mx-3 px-6 py-7 rounded-xl",
          "bg-primary text-primary-foreground hover:bg-primary/90", // Standard shadcn primary button
          "active:scale-95 duration-300" // Keep original active animation
        )}
        onClick={() => logout(router)}
      >
        <span className="text-lg font-semibold text-start whitespace-nowrap">
          {t("logout")}
        </span>
      </Button>
    </div>
  );
}
