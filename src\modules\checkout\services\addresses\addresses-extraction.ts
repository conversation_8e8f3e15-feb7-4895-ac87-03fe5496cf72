import { GET } from "@/lib/http-methods";
import { AxiosError } from "axios";
import {
  AddressResponseDataType,
  AddressType,
} from "@/modules/checkout/types/addresses";
import extractJWTokens from "@/modules/auth/utils/jwt/extract-tokens";
import { refreshToken } from "@/modules/auth/services/refresh-token";
import { castToAddressType } from "../../utils/types-casting/addresses";
import { getBackendLocaleOnParams } from "@/utils/backend-locale";

interface Params {
  locale?: string;
}

export async function retrieveUserAddresses({
  locale,
}: Params): Promise<AddressType[] | null> {
  const { access } = extractJWTokens();
  const header = {
    Authorization: `Bearer ${access}`,
  };

  const params = [];

  if (locale) params.push(getBackendLocaleOnParams({ locale }));

  try {
    const res = await GET(
      `${process.env.BACKEND_ADDRESS}/addresses?${params.join("&")}`,
      header
    );

    return (res.data as AddressResponseDataType[]).map((address) =>
      castToAddressType(address)
    );
  } catch (error) {
    const axiosError = error as AxiosError;

    if (axiosError.response?.status === 401) {
      const res = await refreshToken(() => retrieveUserAddresses({ locale }));

      //unauthorized user error is already handled by the user hook
      if (!res) return [];
      return res;
    }

    return [];
  }
}
