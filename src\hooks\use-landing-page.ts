import getLandingPageContent from "@/services/page-palette/landing-page";
import { LandingPageContent } from "@/types";
import { useQuery } from "@tanstack/react-query";
import useBackendLocale from "./use-backend-locale";

export default function useLandingPage() {
  const { backendLocale } = useBackendLocale();

  const { data, isLoading } = useQuery<LandingPageContent | null>({
    queryKey: ["landing-page", backendLocale],
    queryFn: () => getLandingPageContent({ locale: backendLocale }),
  });

  return {
    isLoading: isLoading || data === undefined,
    landingPageContent: data,
  };
}
