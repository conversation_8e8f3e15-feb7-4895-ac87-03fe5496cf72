import { useEffect, useState } from "react";
import { getProductPageUrl } from "../../utils/urls";
import { ProductType } from "../../types/products";

export default function useProductUrl(product: ProductType | null) {
  const [productPageUrl, setProductPageUrl] = useState<string>("");

  useEffect(() => {
    if (product) setProductPageUrl(getProductPageUrl(product.slug, product.id));
  }, [product]);

  return productPageUrl;
}
