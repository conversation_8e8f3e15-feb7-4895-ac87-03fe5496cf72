import { RadioGroupItem, RadioGroup } from "@/components/ui/radio-group";
import useAddresses from "../../hooks/addresses/use-addresses";
import useAddressSelection from "../../store/address-selection-store";
import { cn } from "@/lib/utils";
import {
  AddressSelectionContainer,
  AddressSelectionContainerSkeleton,
} from "./container/adress-selection";
import { useTranslations } from "next-intl";
import useUserStore from "@/modules/auth/store/user-store";
import { Skeleton } from "@/components/ui/skeleton";

export default function AddressesSelection() {
  const { addresses, addressesAreLoading } = useAddresses();
  const { selectedAddressId, selectAddress } = useAddressSelection();
  const t = useTranslations("modules.checkout.adressess");
  const user = useUserStore((store) => store.user);

  return user && user.isAuthenticated ? (
    !addressesAreLoading ? (
      <RadioGroup
        value={selectedAddressId}
        onValueChange={(value) => {
          selectAddress(value);
        }}
        className={cn({
          "space-y-8": addresses && addresses.length > 0,
        })}
      >
        {addresses ? (
          <div className="space-y-5">
            {addresses.map((address) => (
              <div key={address.id} className="">
                <label htmlFor={address.id} className="">
                  <AddressSelectionContainer
                    address={address}
                    isSelected={address.id === selectedAddressId}
                  />
                </label>
                <RadioGroupItem
                  id={address.id}
                  value={address.id}
                  className="appearance-none hidden"
                />
              </div>
            ))}
          </div>
        ) : null}

        <div className={cn("flex items-center space-x-2")}>
          <RadioGroupItem
            id="newAddress"
            value=""
            className="accent-primary w-[22px] h-[22px] bg-transparent border-primary text-primary fill-primary"
          />
          <label htmlFor="newAddress" className="">
            <h3>{t("newAddress")}</h3>
          </label>
        </div>
      </RadioGroup>
    ) : (
      <div className="flex flex-col space-y-4">
        <Skeleton className="w-[200px] h-6" />
        <div className="flex flex-col space-y-6">
          {Array.from({ length: 3 }).map((_, idx) => (
            <AddressSelectionContainerSkeleton key={idx} />
          ))}
        </div>
      </div>
    )
  ) : null;
}
