import type { NextConfig } from "next";
import createNextIntlPlugin from "next-intl/plugin";

const withNextIntl = createNextIntlPlugin();

const nextConfig: NextConfig = {
  /* config options here */
  env: {
    BACKEND_ADDRESS: "https://api-parastore.tdg.tn",
    PRIVATE_ACCESS_BACKEND_ADDRESS: "https://www.test.tawer.tn",
    FRONTEND_ADDRESS: "http://localhost:3000",
    GOOGLE_CLIENT_ID: "",
    MEASUREMENT_ID: "",
    NEXT_PUBLIC_ENV: "preprod",
  },
  images: {
    domains: ["api-parastore.tdg.tn"],
  },
};

module.exports = withNextIntl(nextConfig);
