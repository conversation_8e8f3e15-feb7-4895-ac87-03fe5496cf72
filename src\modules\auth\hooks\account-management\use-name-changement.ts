import { useState } from "react";
import { useTranslations } from "next-intl";
import { useQueryClient } from "@tanstack/react-query";
import { useForm } from "react-hook-form";
import z from "zod";
import { zodResolver } from "@hookform/resolvers/zod";

import { changeNameOnServerSide } from "../../services/name-changement";
import { CustomError } from "@/utils/custom-error";
import { getNameChangementSchema } from "../../validation/schemas/name-changement";

export default function useNameChangement() {
  const t = useTranslations("modules.auth.validations");
  const errorsContent = useTranslations("modules.auth.errors");
  const queryClient = useQueryClient();

  const [isPending, setIsPending] = useState(false);
  const [nameChanged, setNameChanged] = useState(false);
  const [error, setError] = useState("");

  const nameChangementSchema = getNameChangementSchema(t);
  const form = useForm<z.infer<typeof nameChangementSchema>>({
    resolver: zodResolver(nameChangementSchema),
    defaultValues: {
      name: "",
    },
  });

  async function onSubmit(values: z.infer<typeof nameChangementSchema>) {
    if (error !== "") setError("");

    setIsPending(true);

    try {
      const res = await changeNameOnServerSide({ name: values.name });

      setNameChanged(true);
      form.reset();

      setTimeout(() => {
        setNameChanged(false);
      }, 4000);

      queryClient.invalidateQueries({ queryKey: ["user-data"] });
    } catch (e) {
      const error = e as CustomError;

      if (error.status === 400) {
        if (error.code === "P2011")
          setError(errorsContent("featureNotAvailableForFacebookOrGoogle"));
        else setError(errorsContent("invalidData"));
      } else if (error.status === 404) {
        setError(errorsContent("notFound"));
      } else {
        setError(errorsContent("technicalIssue"));
      }
    } finally {
      setIsPending(false);
    }
  }

  return {
    isPending,
    onSubmit,
    form,
    error,
    nameChanged,
  };
}
