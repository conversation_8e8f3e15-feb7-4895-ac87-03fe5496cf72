import { GET } from "@/lib/http-methods";
import { AxiosError } from "axios";
import { refreshToken } from "@/modules/auth/services/refresh-token";
import { UserType, UserInResponseType } from "@/modules/auth/types";
import { castToUserType } from "@/modules/auth/utils/data-utils/types-casting/user";
import extractJWTokens from "@/modules/auth/utils/jwt/extract-tokens";

export async function retrieveUserDetails(): Promise<UserType | null> {
  const { access } = extractJWTokens();
  const header = {
    Authorization: `Bearer ${access}`,
  };

  try {
    const res = await GET(`${process.env.BACKEND_ADDRESS}/users/me`, header);
    const userData = res.data as UserInResponseType;

    return castToUserType(userData);
  } catch (error) {
    const axiosError = error as AxiosError;

    if (axiosError.response?.status === 401) {
      const res = await refreshToken(retrieveUserDetails);

      return res;
    }

    return null;
  }
}
