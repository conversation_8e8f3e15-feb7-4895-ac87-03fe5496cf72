import { getBackendLocaleOnServerSide } from "@/utils/backend-locale";
import { retrieveCategoriesFromServerSide } from "../services/categories/categories-extraction";
import { retrieveProductsFromServerSide } from "../services/products/products-extraction";
import { ProductType } from "../types/products";

interface Params {
  limit?: number;
}

export default async function retrieveCategoriesProductsFromServerSide({
  limit = 8,
}: Params) {
  const locale = await getBackendLocaleOnServerSide();
  const categories = await retrieveCategoriesFromServerSide({ locale });
  const productsPromises = categories
    ? categories.map(async (productsCategory) => {
        const data = await retrieveProductsFromServerSide({
          locale,
          categoriesSlugs: productsCategory
            ? productsCategory.subCategories.length === 0
              ? [productsCategory.slug]
              : productsCategory.subCategories
                  .flatMap((cat) =>
                    cat.subCategories.length > 0 ? cat.subCategories : [cat]
                  )
                  .map((cat) => cat.slug)
            : [],
          page: 1,
          limit,
        });

        if (data) return data.products;

        return [];
      })
    : [];
  const products: ProductType[] = (await Promise.all(productsPromises)).flat();

  return products;
}
