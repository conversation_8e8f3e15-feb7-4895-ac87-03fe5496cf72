import { MetadataRoute } from "next";

export default function robots(): MetadataRoute.Robots {
  return process.env.NEXT_PUBLIC_ENV === "preprod"
    ? {
        rules: {
          userAgent: "*",
          disallow: "/",
        },
        sitemap: `https://${process.env.FRONTEND_ADDRESS}/sitemap.xml`,
      }
    : {
        rules: [
          {
            userAgent: "*",
            disallow: [
              "/paiement",
              "/mon-compte",
              "/mon-compte/commandes",
              "/mon-compte/info",
              "/mon-compte/adresses",
              "/mon-compte/parametres",
            ],
          },
        ],
        sitemap: `${process.env.FRONTEND_ADDRESS}/sitemap.xml`,
        host: process.env.FRONTEND_ADDRESS,
      };
}
