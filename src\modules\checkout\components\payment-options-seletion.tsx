import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { cn } from "@/lib/utils";
import { useTranslations } from "next-intl";

interface PaymentOptionsSelectionProps {
  value: string;
  onChange: (value: string) => void;
}

export default function PaymentOptionsSelection({
  value,
  onChange,
}: PaymentOptionsSelectionProps) {
  const t = useTranslations("modules.checkout.checkout.fields.payment");

  return (
    <RadioGroup
      value={value}
      onValueChange={onChange}
      className={cn("space-y-3")}
    >
      <Label
        className={cn(
          "border-primary p-4 rounded-[15px] flex items-center space-x-2",
          {
            border: value === "cashOnDelivery",
          }
        )}
      >
        <RadioGroupItem
          value={value}
          className="border border-black text-black"
        />
        <h3 className="font-bold text-black">{t("onDelivery")}</h3>
      </Label>

      {/*
        <label className={cn("flex space-x-2", {
            "space-x-reverse": locale === "ar-LY",
          })}>
          <RadioGroupItem
            value="card"
            className="accent-secondary w-[22px] h-[22px] rounded-full bg-transparent border border-secondary text-secondary"
          />
          <Text textStyle="TS5">{content.cardPayment}</Text>
        </label>
        */}
    </RadioGroup>
  );
}
