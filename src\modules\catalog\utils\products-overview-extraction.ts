import { getBackendLocaleOnServerSide } from "@/utils/backend-locale";
import { retrieveProductsFromServerSide } from "../services/products/products-extraction";
import { retrieveSimilarProductsFromServerSide } from "../services/products/similar-products-extraction";
import { CriteriaType } from "../types";

interface Params {
  categoriesSlugs?: string[];
  similarProductSlug?: string;
  criteria: CriteriaType;
}

export default async function extractProductsOverview({
  criteria,
  categoriesSlugs,
  similarProductSlug,
}: Params) {
  const locale = await getBackendLocaleOnServerSide();

  return similarProductSlug
    ? retrieveSimilarProductsFromServerSide({
        page: 1,
        limit: 8,
        slug: similarProductSlug,
        locale,
      })
    : retrieveProductsFromServerSide({
        page: 1,
        limit: 8,
        criteria,
        categoriesSlugs: categoriesSlugs,
        locale,
      });
}
