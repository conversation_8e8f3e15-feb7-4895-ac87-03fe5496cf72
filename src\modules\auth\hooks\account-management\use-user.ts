import { retrieveUserDetails } from "@/modules/auth/services/user-details-extraction";
import { keepPreviousData, useQuery } from "@tanstack/react-query";
import { usePathname } from "next/navigation";
import { useEffect } from "react";
import useUserStore from "../../store/user-store";
import useAuthRefresher from "../../store/auth-refresher";

export default function useUser() {
  const pathname = usePathname();
  const { refreshUserAuthentication, authRefresher } = useAuthRefresher(
    (store) => store
  );
  const { data, isLoading } = useQuery({
    queryKey: ["user-data", pathname, authRefresher],
    queryFn: () => retrieveUserDetails(),
    placeholderData: keepPreviousData,
  });
  const {
    setUser,
    setIsLoading: setUserIsLoading,
    user,
  } = useUserStore((store) => store);

  useEffect(() => {
    if (data) setUser(data);

    return () => {
      setUser(null);
    };
  }, [data, isLoading]);

  useEffect(() => {
    setUserIsLoading(isLoading);
  }, [isLoading]);

  return {
    user: data ? data : null,
    isLoading,
  };
}
