import { POST } from "@/lib/http-methods";
import extractJWTokens from "@/modules/auth/utils/jwt/extract-tokens";
import { AxiosHeaders, AxiosResponse } from "axios";
import setupTokens from "../utils/jwt/setup-tokens";

export async function refreshToken(onSuccess: () => any) {
  const { refresh } = extractJWTokens();
  const headers = {} as AxiosHeaders;

  try {
    const res: AxiosResponse = await POST(
      `${process.env.BACKEND_ADDRESS}/tokens/refresh`,
      headers,
      { token: refresh }
    );

    setupTokens({ access: res.data.access });

    return onSuccess();
  } catch {
    return null;
  }
}
