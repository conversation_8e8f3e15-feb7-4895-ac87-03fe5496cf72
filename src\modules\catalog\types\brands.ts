import { SeoMetaContentType, SeoMetaContentTypeInResponse } from "@/types";

export interface BrandType {
  id: string;
  slug: string;
  name: string;
  image: string;
  numberOfProducts: number;
  metaContent: SeoMetaContentType | null;
}
export interface BrandInResponseType {
  id: string;
  slug: string;
  name: string;
  image: string;
  numberOfProducts: number;

  metaContent: SeoMetaContentTypeInResponse | null;
}
