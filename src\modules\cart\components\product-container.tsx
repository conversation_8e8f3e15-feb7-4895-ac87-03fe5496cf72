import { CircleX, Minus, Plus } from "lucide-react";
import Image from "next/image";
import { ProductItemType } from "../types/products";
import Text from "@/styles/text-styles";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { useCartStore } from "../store/cart-store";
import { formatPrice } from "@/modules/catalog/utils/prices-transformation";
import { useEffect, useState } from "react";

interface Props {
  productItem: ProductItemType;
}

export default function ProductContainer({ productItem }: Props) {
  const [productImage, setProductImage] = useState(
    "/not-found/product-image.webp"
  );

  const { updateProductItemQuantity, removeProductItem } = useCartStore(
    (store) => store.actions
  );

  const promotionIsAvailable =
    productItem?.prices[0].realPrice !==
    productItem?.prices[0].promotionalPrice;

  useEffect(() => {
    if (productItem) setProductImage(productItem.image);
  }, [productItem]);

  return (
    <div className="w-full flex gap-4">
      <Image
        src={productImage}
        alt={productItem.name}
        onError={() => setProductImage("/not-found/product-image.webp")}
        width={80}
        height={80}
        unoptimized
        className="h-20 w-20 rounded-lg overflow-hidden border border-primary object-contain"
      />

      <div className="w-full flex-1 space-y-2">
        <div className="flex justify-between font-medium">
          <h4 className="line-clamp-1 max-w-[80%]">
            <Text textStyle="TS7">{productItem.name}</Text>
          </h4>
          <Button
            size={"tiny"}
            variant="ghost"
            onClick={() => removeProductItem(productItem.id)}
            className="text-gray"
          >
            <CircleX className="h-4 w-4" />
          </Button>
        </div>

        <div className="flex-1 flex items-end justify-between space-x-2">
          <div className="flex flex-col">
            {promotionIsAvailable && (
              <Text textStyle="TS7" className="text-primary-darkine-through">
                {`${formatPrice(productItem.prices[0].realPrice)} ${
                  productItem.prices[0].currency
                } `}
              </Text>
            )}

            <Text
              textStyle="TS6"
              className="text-primary font-medium"
            >{`${productItem.prices[0].promotionalPrice} ${productItem.prices[0].currency}`}</Text>
          </div>

          <div className="border border-primary rounded-2xl w-fit px-2 py-[6px] flex items-center gap-2 mt-2">
            <Button
              size={"tiny"}
              variant="ghost"
              className={cn("text-primary border border-primary rounded-full", {
                "opacity-50": productItem.cartQuantity === 1,
              })}
              onClick={() =>
                updateProductItemQuantity(
                  productItem.id,
                  productItem.cartQuantity - 1
                )
              }
            >
              <Minus className="h-4 w-4" />
            </Button>

            <Text
              textStyle="TS8"
              className="text-center text-primary font-bold"
            >
              {productItem.cartQuantity}
            </Text>

            <Button
              size={"tiny"}
              variant="ghost"
              className=" text-primary border border-primary rounded-full"
              onClick={() =>
                updateProductItemQuantity(
                  productItem.id,
                  productItem.cartQuantity + 1
                )
              }
            >
              <Plus className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
