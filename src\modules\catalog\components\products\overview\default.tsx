"use client";
import { type HTMLAttributes } from "react";
import type { ProductsSectionsVariant } from "../../../types";
import { getCriteriaBasedOnProductsVariant } from "@/modules/catalog/utils/criteria-based-on-variant";
import { useTranslations } from "next-intl";
import useProducts from "@/modules/catalog/hooks/products/use-products";
import ProductsOverviewUI from "./ui";
import { useWindowWidth } from "@react-hook/window-size";
import { STATIC_URLS } from "@/utils/urls";

interface Props extends HTMLAttributes<"html"> {
  maxProductsNumber?: number;
  variant?: ProductsSectionsVariant;
}

export default function ProductsOverview({
  variant = "default",
  maxProductsNumber = 8,
  ...props
}: Props) {
  const t = useTranslations("modules.catalog.products.overview");
  const screenWidth = useWindowWidth();

  const { products, productsAreLoading } = useProducts({
    limit: screenWidth < 768 ? 8 : 7,
    queryKeys: [screenWidth],
    criteria: getCriteriaBasedOnProductsVariant(variant),
  });

  const moreProductsLink =
    variant === "news"
      ? `${STATIC_URLS["products"].store}?sort=newer`
      : variant === "mostSold"
      ? "${?criteria=mostSold"
      : `${STATIC_URLS["products"].store}`;

  return (
    <ProductsOverviewUI
      title={
        variant === "news"
          ? t("news.title")
          : variant === "mostSold"
          ? t("mostSold.title")
          : t("default.subtitle")
      }
      subtitle={
        variant === "news"
          ? t("news.subtitle")
          : variant === "mostSold"
          ? t("mostSold.subtitle")
          : t("default.subtitle")
      }
      products={products}
      isLoading={productsAreLoading}
      checkMoreButton={{
        content: t("buttons.discoverPlus"),
        link: moreProductsLink,
      }}
    />
  );
}
