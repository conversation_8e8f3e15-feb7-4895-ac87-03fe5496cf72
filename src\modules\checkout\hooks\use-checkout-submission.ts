"use client";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useRouter } from "next/navigation";
import { useTranslations } from "next-intl";
import { useCartStore } from "@/modules/cart/store/cart-store";
import { useCheckoutStore } from "../store/checkout-store";
import { useAuthDialogState } from "@/modules/auth/store/auth-dialog-state-store";
import useAddressCreationForm from "./addresses/use-address-creation-form";
import {
  registerGuestOrderOnServerSide,
  registerUserOrderOnServerSide,
} from "../services/orders/order-creation";
import {
  RegisterGuestOrderRequestParams,
  RegisterUserOrderRequestParams,
} from "../types/orders";
import { CustomError } from "../../../utils/custom-error";
import useValidatedCouponCode from "@/modules/coupon-codes/store/coupon-code-validation";
import { getCheckoutFormSchema } from "../validation/schemas/checkout-submission";

interface Params {
  user: {
    email: string;
    name: string;
    role: string;
    isAuthenticated: boolean;
    points: number;
  } | null;
  selectedAddressId: string;
}

export default function useCheckoutSubmission({
  user,
  selectedAddressId,
}: Params) {
  const [error, setError] = useState<string>("");
  const [isPending, setIsPending] = useState(false);

  const router = useRouter();
  const t = useTranslations("modules.checkout.validations");
  const errorsContent = useTranslations("modules.checkout.errors");

  const { state, actions } = useCartStore((store) => store);
  const { notes: storeNotes, setNotes } = useCheckoutStore();
  const validatedCouponCode = useValidatedCouponCode(
    (store) => store.validatedCouponCode
  );
  const setAuthDialogState = useAuthDialogState((store) => store.setIsOpen);

  const {
    form: addressForm,
    exctractInfoForGuestCheckout,
    onSubmit: submitAddress,
  } = useAddressCreationForm({
    useAddressIdInCheckout: true,
  });

  const checkoutSchema = getCheckoutFormSchema(t);
  const form = useForm<z.infer<typeof checkoutSchema>>({
    resolver: zodResolver(checkoutSchema),
    defaultValues: {
      notes: storeNotes || "",
      paymentMethod: "",
    },
  });

  // Sync form notes with store
  const notesValue = form.watch("notes");
  if (notesValue !== storeNotes) {
    setNotes(notesValue || "");
  }

  const checkoutOnServerSide = async (
    address: Record<string, string> | null,
    notes: string,
    addressId: string
  ) => {
    try {
      if (addressId === "" && !address) {
        throw new Error("missedData");
      }

      setIsPending(true);
      setError("");

      let orderId = "";

      if (user && user.isAuthenticated) {
        const orderInfo: RegisterUserOrderRequestParams = {
          items: state.cartItems.map((item) => ({
            productItemId: item.id,
            quantity: item.cartQuantity,
          })),
          notes,
        };

        if (validatedCouponCode && validatedCouponCode.code !== "") {
          orderInfo["voucherCode"] = validatedCouponCode.code;
        }

        if (addressId !== "") {
          orderInfo["addressId"] = addressId;
        } else {
          throw new Error("missedData");
        }

        const res = await registerUserOrderOnServerSide(orderInfo);
        orderId = res.id;
      } else if (address) {
        const orderInfo: RegisterGuestOrderRequestParams = {
          items: state.cartItems.map((item) => ({
            productItemId: item.id,
            quantity: item.cartQuantity,
          })),
          address,
          notes,
        };

        if (validatedCouponCode && validatedCouponCode.code !== "") {
          orderInfo["voucherCode"] = validatedCouponCode.code;
        }

        const res = await registerGuestOrderOnServerSide(orderInfo);
        orderId = res.id;
      } else {
        throw new Error("missedData");
      }

      actions.emptyCart();
      router.push(`/commandes/${orderId}`);
    } catch (error) {
      const customError = error as CustomError;

      if (customError.message === "missedData") {
        setError(errorsContent("missedData"));
        const addressSection = document.getElementById("addressCreation");
        if (addressSection) {
          addressSection.scrollIntoView({ behavior: "smooth" });
        }
      } else if (customError.status === 401) {
        setAuthDialogState(true);
      } else if (customError.code === "P9000") {
        setError(errorsContent("productItemOutOfStock"));
      } else {
        setError(errorsContent("technicalIssue"));
      }
    } finally {
      setIsPending(false);
    }
  };

  async function onSubmit(values: z.infer<typeof checkoutSchema>) {
    try {
      if (user && user.isAuthenticated) {
        if (selectedAddressId === "") {
          await submitAddress(addressForm.getValues());

          if (selectedAddressId !== "") {
            await checkoutOnServerSide(
              null,
              values.notes || "",
              selectedAddressId
            );
          }

          return;
        } else {
          await checkoutOnServerSide(
            null,
            values.notes || "",
            selectedAddressId
          );
        }
      } else {
        await checkoutOnServerSide(
          exctractInfoForGuestCheckout(),
          values.notes || "",
          selectedAddressId
        );
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "An error occurred");
    }
  }

  return {
    form,
    error,
    onSubmit,
    isPending,
  };
}
