import { GET } from "@/lib/http-methods";
import { refreshToken } from "@/modules/auth/services/refresh-token";
import extractJWTokens from "@/modules/auth/utils/jwt/extract-tokens";
import { CustomError } from "@/utils/custom-error";
import { AxiosError } from "axios";
import castToCartItem from "../utils/types-casting/cart-items";
import { ProductInResponse } from "../types/products";

interface Params {
  locale?: string;
}

export async function extractCartItems({ locale }: Params) {
  const { access } = extractJWTokens();
  const headers = {
    Authorization: `Bearer ${access}`,
  };

  try {
    const res = await GET("/carts/products", headers);
    const cartItems = (res.data as ProductInResponse[]).map((cartItem) =>
      castToCartItem(cartItem)
    );

    return cartItems || [];
  } catch (error) {
    const axiosError = error as AxiosError;
    if (axiosError.response?.status == 401) {
      const res = await refreshToken(() => extractCartItems({ locale }));
      if (!res) throw new CustomError("Unauthorized", 401);

      return res;
    } else throw new CustomError("Server Error!", 500);
  }
}
