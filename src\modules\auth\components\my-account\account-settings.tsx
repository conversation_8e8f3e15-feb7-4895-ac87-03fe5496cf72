"use client";
import { Skeleton } from "@/components/ui/skeleton";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"; // Import Form components
import { useTranslations } from "next-intl";
import { cn } from "@/lib/utils";
import useUserStore from "@/modules/auth/store/user-store";
import usePasswordChangement from "../../hooks/account-management/use-password-changement";

export default function AccountSettings() {
  const { isLoading: userIsLoading } = useUserStore((store) => store);
  const t = useTranslations("accountPage.accountSettings");
  const { isPending, onSubmit, form, passwordChanged, error } =
    usePasswordChangement();

  return !userIsLoading ? (
    <div className={cn("flex flex-col space-y-16")}>
      <div className={cn("flex flex-col space-y-7")}>
        <h2 className="text-xl font-bold text-primary">{t("title")}</h2>
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(onSubmit)}
            className="flex flex-col space-y-7 max-w-lg"
          >
            {error && (
              <p className="text-sm text-destructive text-center">{error}</p>
            )}
            <FormField
              control={form.control}
              name="currentPassword"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-base text-primary">
                    {t("previousPassword")}
                  </FormLabel>
                  <FormControl>
                    <Input
                      className={cn(
                        "text-primary md:min-w-[350px] lg:min-w-[450px] border border-primary rounded-lg"
                      )}
                      type="password"
                      placeholder="*********"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="newPassword"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-base text-primary">
                    {t("newPassword")}
                  </FormLabel>
                  <FormControl>
                    <Input
                      className={cn(
                        "text-primary md:min-w-[350px] lg:min-w-[450px] border border-primary rounded-lg"
                      )}
                      type="password"
                      placeholder="*********"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="confirmationPassword"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-base text-primary">
                    {t("confirmationPassword")}
                  </FormLabel>
                  <FormControl>
                    <Input
                      className={cn(
                        "text-primary md:min-w-[350px] lg:min-w-[450px] border border-primary rounded-lg"
                      )}
                      type="password"
                      placeholder="*********"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <Button
              type="submit"
              onClick={form.handleSubmit(onSubmit)} // Ensure handleSubmit is called
              className={cn(
                "md:w-fit w-full h-11 lg:min-w-[450px] md:min-w-[350px] rounded-xl flex justify-center items-center active:scale-95 group bg-primary text-primary-foreground hover:bg-primary/90",
                {
                  "opacity-50 cursor-not-allowed": isPending, // Disable visual feedback
                }
              )}
              disabled={isPending}
            >
              <span className="text-base font-medium">
                {isPending
                  ? "Changing..."
                  : passwordChanged
                  ? t("buttons.changementConfirmed")
                  : t("buttons.button")}
              </span>
            </Button>
          </form>
        </Form>
      </div>
    </div>
  ) : (
    <div className="flex flex-col space-y-16">
      <Skeleton className="w-52 h-10" />
      <div className={cn("flex flex-col space-y-14")}>
        <Skeleton className="md:w-72 w-52 h-10" />
        <div className="flex flex-col space-y-7">
          <div className="w-full flex flex-col space-y-2">
            <Skeleton className="w-40 h-10" />
            <Skeleton className="md:w-80 w-full h-10" />
          </div>
          <div className="w-full flex flex-col space-y-2">
            <Skeleton className="w-40 h-10" />
            <Skeleton className="md:w-80 w-full h-10" />
          </div>
          <div className="w-full flex flex-col space-y-2">
            <Skeleton className="w-40 h-10" />
            <Skeleton className="md:w-80 w-full h-10" />
          </div>
          <Skeleton className="md:w-80 w-full h-10" />
        </div>
      </div>
    </div>
  );
}
