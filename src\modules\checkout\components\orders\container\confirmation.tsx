"use client";
import { OrderDataType } from "../../../types/orders";
import { useTranslations } from "use-intl";
import useCurrency from "@/modules/catalog/hooks/use-currency";

type Props = {
  details: OrderDataType;
};

const OrderConfirmationContainer = ({ details }: Props) => {
  const t = useTranslations("ordersManagement");
  const { currency } = useCurrency();

  return (
    <div className="XL:max-w-[600px] max-w-[500px] w-full XL:p-4 L:p-3 p-2 h-fit flex bg-primary/50 XL:rounded-[33px] L:rounded-[25px] rounded-[18px] text-primary bg-primary-light border border-primary justify-between">
      <div className="space-y-4">
        <div className="flex flex-col space-y-2">
          <h3 className="font-bold block text-base">
            {t("order.labels.number")}
          </h3>
          <p className="block font-thin text-base">{details.code}</p>
        </div>
        <div>
          <h3 className="font-bold block text-base">
            {t("order.labels.orderedProducts")}
          </h3>
          {details.items.map((item, idx) => (
            <p key={idx} className="block font-thin text-base">
              {`${item.name} X ${item.quantity} → `}
              {item.price != item.promotionnalPrice ? (
                <>
                  <span className="line-through text-red-500 mr-1">
                    {item.price.toFixed(3)}
                    {currency}
                  </span>
                  <span className="text-primary-600">
                    {item.promotionnalPrice.toFixed(3)}
                    {currency}
                  </span>
                </>
              ) : (
                `${(item.price * item.quantity).toFixed(3)}${currency}`
              )}
            </p>
          ))}
        </div>
        <div className="flex flex-col space-y-2">
          <p className="font-bold block text-base">
            {`${
              details.discount !== 0
                ? t("order.labels.subTotalWithoutDiscount")
                : t("order.labels.subTotal")
            }: ${details.total.toFixed(3)} ${currency}`}
          </p>
          {details.discount !== 0 && (
            <p className="font-bold block text-base">
              {`${t("order.labels.discount")}: ${details.discount.toFixed(
                3
              )} ${currency}`}
            </p>
          )}
          <p className="font-bold block text-base">
            {`${t("order.labels.shippingCost")}: ${details.shippingCost.toFixed(
              3
            )} ${currency}`}
          </p>
          <p className="font-bold block text-base">
            {`${
              details.discount !== 0
                ? t("order.labels.subTotalWithDiscount")
                : t("order.labels.total")
            }: ${(
              details.total +
              details.shippingCost -
              details.discount
            ).toFixed(3)} ${currency}`}
          </p>
        </div>
        <div className="flex flex-col space-y-2">
          <h3 className="font-bold block text-base">
            {t("order.labels.shippingAddress")}
          </h3>
          <div className="flex flex-col">
            <p className="text-base">
              {`${details.address.firstName} ${details.address.lastName}`}
            </p>
            {details.address.company ? (
              <p className="text-base">{details.address.company}</p>
            ) : null}
            <p className="text-base">{details.address.phone}</p>
            <p className="text-base">{`${details.address.address1} ${
              details.address.city.country.name
            } ${details.address.postalCode ? details.address.postalCode : ""} ${
              details.address.city.name
            }`}</p>
            {details.address.address2 ? (
              <p className="text-base">{details.address.address2}</p>
            ) : null}
          </div>
        </div>
        <div className="space-y-2">
          <h3 className="font-bold block text-base">
            {t("order.labels.status")}
          </h3>
          <span className="font-normal text-white bg-primary rounded-3xl block py-1 px-3 w-fit text-base">
            {details.status}
          </span>
        </div>
      </div>
    </div>
  );
};

export default OrderConfirmationContainer;
