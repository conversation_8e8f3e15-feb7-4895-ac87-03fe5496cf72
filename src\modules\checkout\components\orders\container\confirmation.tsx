"use client";
import Text from "@/styles/text-styles";
import { OrderDataType } from "../../../types/orders";
import { useTranslations } from "use-intl";
import useCurrency from "@/modules/catalog/hooks/use-currency";

type Props = {
  details: OrderDataType;
};

const OrderConfirmationContainer = ({ details }: Props) => {
  const t = useTranslations("ordersManagement");
  const { currency } = useCurrency();

  return (
    <div className="XL:max-w-[600px] max-w-[500px] w-full XL:p-4 L:p-3 p-2 h-fit flex bg-primary/50 XL:rounded-[33px] L:rounded-[25px] rounded-[18px] text-primary bg-primary-light border border-primary justify-between">
      <div className="space-y-4">
        <div className="flex flex-col space-y-2">
          <Text textStyle="TS6" className="font-bold block">
            {t("order.labels.number")}
          </Text>
          <Text textStyle="TS6" className="block font-thin">
            {details.code}
          </Text>
        </div>
        <div>
          <Text textStyle="TS6" className="font-bold block">
            {t("order.labels.orderedProducts")}
          </Text>
          {details.items.map((item, idx) => (
            <Text key={idx} textStyle="TS6" className="block font-thin">
              {`${item.name} X ${item.quantity} → `}
              {item.price != item.promotionnalPrice ? (
                <>
                  <span className="line-through text-red-500 mr-1">
                    {item.price.toFixed(3)}
                    {currency}
                  </span>
                  <span className="text-primary-600">
                    {item.promotionnalPrice.toFixed(3)}
                    {currency}
                  </span>
                </>
              ) : (
                `${(item.price * item.quantity).toFixed(3)}${currency}`
              )}
            </Text>
          ))}
        </div>
        <div className="flex flex-col space-y-2">
          <Text textStyle="TS6" className="font-bold block">
            {`${
              details.discount !== 0
                ? t("order.labels.subTotalWithoutDiscount")
                : t("order.labels.subTotal")
            }: ${details.total.toFixed(3)} ${currency}`}
          </Text>
          {details.discount !== 0 && (
            <Text textStyle="TS6" className="font-bold block">
              {`${t("order.labels.discount")}: ${details.discount.toFixed(
                3
              )} ${currency}`}
            </Text>
          )}
          <Text textStyle="TS6" className="font-bold block">
            {`${t("order.labels.shippingCost")}: ${details.shippingCost.toFixed(
              3
            )} ${currency}`}
          </Text>
          <Text textStyle="TS6" className="font-bold block">
            {`${
              details.discount !== 0
                ? t("order.labels.subTotalWithDiscount")
                : t("order.labels.total")
            }: ${(
              details.total +
              details.shippingCost -
              details.discount
            ).toFixed(3)} ${currency}`}
          </Text>
        </div>
        <div className="flex flex-col space-y-2">
          <Text textStyle="TS6" className="font-bold block">
            {t("order.labels.shippingAddress")}
          </Text>
          <div className="flex flex-col">
            <Text textStyle="TS6">
              {`${details.address.firstName} ${details.address.lastName}`}
            </Text>
            {details.address.company ? (
              <Text textStyle="TS6">{details.address.company}</Text>
            ) : null}
            <Text textStyle="TS6">{details.address.phone}</Text>
            <Text textStyle="TS6">{`${details.address.address1} ${
              details.address.city.country.name
            } ${details.address.postalCode ? details.address.postalCode : ""} ${
              details.address.city.name
            }`}</Text>
            {details.address.address2 ? (
              <Text textStyle="TS6">{details.address.address2}</Text>
            ) : null}
          </div>
        </div>
        <div className="space-y-2">
          <Text textStyle="TS6" className="font-bold block">
            {t("order.labels.status")}
          </Text>
          <Text
            textStyle="TS6"
            className="font-normal text-white bg-primary rounded-3xl block py-1 px-3 w-fit"
          >
            {details.status}
          </Text>
        </div>
      </div>
    </div>
  );
};

export default OrderConfirmationContainer;
