import { GET } from "@/lib/http-methods";
import { City, CityResponseDataType } from "@/modules/checkout/types/addresses";
import { castToCityType } from "@/modules/checkout/utils/types-casting/addresses";
import { getBackendLocaleOnParams } from "@/utils/backend-locale";

interface Params {
  countryCode: string;
  locale?: string;
}

export async function retrieveCities({
  countryCode,
  locale,
}: Params): Promise<City[]> {
  const params = [];

  if (locale) params.push(getBackendLocaleOnParams({ locale }));

  try {
    const res = await GET(
      `${
        process.env.BACKEND_ADDRESS
      }/addresses/cities/${countryCode}}?${params.join("&")}`,
      {}
    );
    return (res.data as CityResponseDataType[]).map((city) =>
      castToCityType(city)
    );
  } catch (error) {
    return [];
  }
}
