import { POST } from "@/lib/http-methods";

import { AxiosError } from "axios";
import { AddressType } from "../../types/addresses";
import extractJWTokens from "@/modules/auth/utils/jwt/extract-tokens";
import { CustomError } from "@/utils/custom-error";
import { refreshToken } from "@/modules/auth/services/refresh-token";

type ResponseType = {
  ok: boolean;
  address?: AddressType;
  status: number;
  error?: string;
};

export default async function createAddressOnServerSide(address: {
  [key: string]: string;
}): Promise<ResponseType> {
  const { access } = extractJWTokens();
  const header = {
    Authorization: `Bearer ${access}`,
  };

  try {
    const res = await POST(`/addresses/register`, header, address);

    return {
      ok: true,
      status: 204,
      address: res.data as AddressType,
    };
  } catch (error) {
    const axiosError = error as AxiosError<{ message: string; code: string }>;

    if (axiosError.response?.status === 401) {
      const res = await refreshToken(() => createAddressOnServerSide(address));

      if (!res) return res;
      else throw new CustomError("Unauthorized!", 401);
    }

    const responseStatus = axiosError.response?.status ?? 500;
    const responseCode = axiosError.response?.data.code;
    const errorMessage =
      axiosError.response?.data.message ||
      axiosError.message ||
      "Unknown error";

    throw new CustomError(errorMessage, responseStatus, responseCode);
  }
}
