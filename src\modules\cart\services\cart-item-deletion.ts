import { DELETE } from "@/lib/http-methods";
import { refreshToken } from "@/modules/auth/services/refresh-token";
import extractJWTokens from "@/modules/auth/utils/jwt/extract-tokens";
import { AxiosError } from "axios";
import { CustomError } from "@/utils/custom-error";

export async function deleteCartItemOnServerSide(itemId: string) {
  const { access } = extractJWTokens();
  const headers = {
    Authorization: `Bearer ${access}`,
  };

  try {
    await DELETE(`/carts/products/${itemId}`, headers);
    return { ok: true };
  } catch (error) {
    const axiosError = error as AxiosError;
    if (axiosError.response?.status == 401) {
      const res = await refreshToken(() => deleteCartItemOnServerSide(itemId));
      if (!res) throw new CustomError("Unauthorized", 401);
    } else if (axiosError.response?.status == 404) {
      throw new CustomError("Product Not Found!", 404);
    } else throw new CustomError("Server Error!", 500);
  }
}
