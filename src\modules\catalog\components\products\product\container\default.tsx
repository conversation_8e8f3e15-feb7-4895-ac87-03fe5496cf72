"use client";
import type { ProductType } from "../../../../types/products";
import { Skeleton } from "@/components/ui/skeleton";
import { Card, CardContent } from "@/components/ui/card"; // Removed CardFooter
import { useTranslations } from "next-intl";
import { getPromotionPercentage } from "../../../../utils/promotion-percentage";
import { useEffect, useState } from "react";
import useCurrency from "@/modules/catalog/hooks/use-currency";
import { getProductPageUrl } from "@/utils/urls";
import Image from "next/image";
import { Button } from "@/components/ui/button";

interface Props {
  product: ProductType | null;
  translateAnimation?: boolean;
}

export default function ProductContainer({
  product,
  translateAnimation = false,
}: Props) {
  const [productImage, setProductImage] = useState(
    "/not-found/product-image.webp"
  );
  const productPageUrl = product
    ? getProductPageUrl(product.slug, product.id)
    : "";
  const discountPercentage = product
    ? getPromotionPercentage(
        product.items[0].prices[0].realPrice,
        product.items[0].prices[0].promotionalPrice
      )
    : 0;
  const t = useTranslations("productPage");
  const { currency } = useCurrency();

  useEffect(() => {
    if (product && product.items.length > 0)
      setProductImage(product.items[0].image);
  }, [product]);

  return product ? (
    <Card
      key={product.id}
      className="group cursor-pointer gap-0 transition-all duration-200 hover:shadow-lg"
    >
      <CardContent>
        <div className="relative bg-muted mb-4 aspect-square overflow-hidden rounded-lg">
          {discountPercentage != 0 && (
            <span className="z-50 absolute left-1 top-1 w-fit h-fit px-1 py-0.5 rounded bg-destructive text-lg text-destructive-foreground">
              {`-${discountPercentage}%`}
            </span>
          )}
          <Image
            src={productImage}
            alt={product.name}
            width={200}
            height={200}
            className="h-full w-full object-cover transition-transform duration-200 group-hover:scale-105"
          />
        </div>

        <div className="space-y-3">
          <h3 className="text-lg leading-tight line-clamp-2 font-semibold">
            {product.name}
          </h3>

          {product.brand && (
            <div className="text-muted-foreground flex items-center gap-2 text-sm">
              <span>{product.brand.name}</span>
            </div>
          )}

          <div className="flex items-center justify-between pt-2">
            <span className="text-xl font-bold">
              {`${product.items[0].prices[0].realPrice.toFixed(2)} ${currency}`}
            </span>
            <Button>Add to Cart</Button>
          </div>
        </div>
      </CardContent>
    </Card>
  ) : (
    // Enhanced Loading Skeleton
    <Card className="relative shadow-sm L:h-[420px] h-[300px] border bg-card rounded-[20px] overflow-hidden">
      <CardContent className="p-0 h-full flex flex-col">
        {/* Image Skeleton */}
        <div className="L:h-[280px] h-[180px] bg-muted/50 rounded-t-[20px] relative">
          <Skeleton className="w-full h-full rounded-t-[20px]" />
          <Skeleton className="absolute top-3 right-3 h-6 w-12 rounded-lg" />
        </div>
        {/* Content Skeleton */}
        <div className="flex-1 flex flex-col justify-between p-4">
          <div className="space-y-2">
            <Skeleton className="h-3 w-16" />
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-3/4" />
          </div>
          <div className="flex items-baseline justify-center space-x-2 mt-3">
            <Skeleton className="h-5 w-20" />
            <Skeleton className="h-4 w-16" />
          </div>
        </div>
      </CardContent>
      <div className="p-3 pt-0">
        {" "}
        {/* Replaced CardFooter with a div */}
        <Skeleton className="w-full h-10 rounded-lg" />
      </div>
    </Card>
  );
}
