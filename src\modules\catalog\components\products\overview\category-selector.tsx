"use client";
import { type HTMLAttributes, useState, useEffect } from "react";
import type { ProductsSectionsVariant } from "../../../types";
import type { CategoryType } from "@/modules/catalog/types/categories";
import { getCriteriaBasedOnProductsVariant } from "@/modules/catalog/utils/criteria-based-on-variant";
import { useTranslations } from "next-intl";
import useProducts from "@/modules/catalog/hooks/products/use-products";
import useCategories from "@/modules/catalog/hooks/categories/use-categories";
import { useWindowWidth } from "@react-hook/window-size";
import ProductsOverviewUI from "./ui";
import { getCategoryPageUrl, STATIC_URLS } from "@/utils/urls";

interface Props extends HTMLAttributes<"html"> {
  maxProductsNumber?: number;
  variant?: ProductsSectionsVariant;
  category?: CategoryType;
}

export default function ProductsCategorySelector({
  variant = "default",
  maxProductsNumber = 8,
  ...props
}: Props) {
  const t = useTranslations("modules.catalog.products.overview");
  const screenWidth = useWindowWidth();

  // Category filtering
  const [selectedCategory, setSelectedCategory] = useState<CategoryType | null>(
    null
  );
  const { categories } = useCategories();
  const [categoriesThatHasProducts, setCategoriesThatHasProducts] = useState<
    CategoryType[]
  >([]);

  //sorting categories to get categories with high number of products first
  useEffect(() => {
    if (categories && categories.length > 0) {
      setCategoriesThatHasProducts(
        categories.sort(
          (prevCat, cat) => cat.numberOfProducts - prevCat.numberOfProducts
        )
      );
    }
  }, [variant, categories]);

  useEffect(() => {
    if (
      categoriesThatHasProducts &&
      categoriesThatHasProducts.length > 0 &&
      !selectedCategory
    ) {
      setSelectedCategory(categoriesThatHasProducts[0]);
    }
  }, [variant, categories, selectedCategory, categoriesThatHasProducts]);

  const { products, productsAreLoading } = useProducts({
    limit: screenWidth < 768 ? 8 : 7,
    queryKeys: [screenWidth],
    criteria: getCriteriaBasedOnProductsVariant(variant),
    categoriesSlugs: selectedCategory
      ? selectedCategory.subCategories.length === 0
        ? [selectedCategory.slug]
        : [
            selectedCategory.slug,
            ...selectedCategory.subCategories
              .flatMap((cat) =>
                cat.subCategories.length > 0 ? cat.subCategories : [cat]
              )
              .map((cat) => cat.slug),
          ]
      : undefined,
  });

  const moreProductsLink = selectedCategory
    ? variant === "news"
      ? `${getCategoryPageUrl(selectedCategory)}?sort=newer`
      : variant === "mostSold"
      ? "${?criteria=mostSold"
      : `${getCategoryPageUrl(selectedCategory)}`
    : STATIC_URLS["products"].store;

  return (
    <ProductsOverviewUI
      title={
        variant === "news"
          ? t("news.title")
          : variant === "mostSold"
          ? t("mostSold.title")
          : t("default.subtitle")
      }
      subtitle={
        variant === "news"
          ? t("news.subtitle")
          : variant === "mostSold"
          ? t("mostSold.subtitle")
          : t("default.subtitle")
      }
      products={products}
      isLoading={productsAreLoading}
      selectedSelectorId={selectedCategory ? selectedCategory.slug : ""}
      checkMoreButton={{
        content: t("buttons.discoverPlus"),
        link: moreProductsLink,
      }}
      selectors={
        (variant === "default" || variant === "mostSold") &&
        categoriesThatHasProducts &&
        categoriesThatHasProducts.length > 0
          ? categoriesThatHasProducts.map((cat) => ({
              name: cat.name,
              id: cat.slug,
              onClick: () => setSelectedCategory(cat),
            }))
          : []
      }
    />
  );
}
