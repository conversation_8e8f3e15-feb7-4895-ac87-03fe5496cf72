import { GET } from "@/lib/http-methods";
import { castToProductType } from "../../utils/types-casting/products";
import { CustomError } from "@/utils/custom-error";
import { AxiosError } from "axios";
import { getBackendLocaleOnParams } from "@/utils/backend-locale";

interface Params {
  slug: string;
  locale?: string;
}

export async function retrieveProductFromServerSide({ slug, locale }: Params) {
  const params = [];

  if (locale) params.push(getBackendLocaleOnParams({ locale }));

  try {
    const res = await GET(`/products/${slug}?${params.join("&")}`, {});

    return castToProductType(res.data);
  } catch (error) {
    const errorResponse = (error as AxiosError).response as {
      data: { message: string };
      status: number;
    };

    throw new CustomError(
      errorResponse.data?.message as string,
      errorResponse.status as number
    );
  }
}
