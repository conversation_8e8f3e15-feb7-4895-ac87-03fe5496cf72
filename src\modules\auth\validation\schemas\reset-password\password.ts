import { z } from "zod";
import { TranslateFunction } from "@/types";

const MIN_PASSWORD_LENGTH = 8;

export function getPasswordSchema(t?: TranslateFunction) {
  return z.object({
    password: z.string().min(MIN_PASSWORD_LENGTH, {
      message: t
        ? t("password.tooShort", { min: MIN_PASSWORD_LENGTH })
        : `Le mot de passe doit contenir au moins ${MIN_PASSWORD_LENGTH} caractères.`,
    }),
  });
}
