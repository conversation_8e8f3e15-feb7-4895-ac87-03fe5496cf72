import { Button } from "@/components/ui/button";
import { useProductsFilteringStore } from "../../store/products-filter";
import { AppliedFilter } from "./applied-filter";
import { useTranslations } from "next-intl";
import { useEffect, useState } from "react";
import { BrandType } from "../../types/brands";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { CategorySelectionType } from "../../types/categories";

export default function AppliedFilters() {
  const t = useTranslations("filtersPage");
  const searchParams = useSearchParams();
  const router = useRouter();
  const pathname = usePathname();

  const [displayedSubCategories, setDisplayedSubCategories] = useState<
    CategorySelectionType[]
  >([]);
  const [displayedcategories, setDisplayedcategories] = useState<
    CategorySelectionType[]
  >([]);
  const [displayedBrands, setDisplayedBrands] = useState<BrandType[]>([]);

  const {
    search,
    joinedPageData,
    selectedBrands,
    categories,
    clearAll: clearAllFilter,
    applyFilter,
  } = useProductsFilteringStore();

  useEffect(() => {
    setDisplayedBrands(selectedBrands);
    setDisplayedcategories(categories.filter((cat) => cat.selected));
    setDisplayedSubCategories(
      categories.flatMap((cat) =>
        cat.subCategories.filter((subCat) => subCat.selected)
      )
    );

    return () => {
      setDisplayedBrands([]);
      setDisplayedSubCategories([]);
      setDisplayedcategories([]);
    };
  }, [categories, selectedBrands]);

  const clearAll = () => {
    const params = new URLSearchParams(searchParams.toString());

    if (search !== "") {
      params.delete("search");
    }

    //deletion of brands , categories and subtypes
    params.delete("brandsIds");
    params.delete("categoriesIds");
    params.delete("subTypesIds");

    const newUrl = `${pathname}?${params.toString()}`;
    router.replace(newUrl);

    clearAllFilter();
    applyFilter();
  };
  const searchAppliedFilterIsDisplayed = search !== "";
  const categoriesAppliedFilterIsDisplayed = displayedcategories.length !== 0;
  const subCategoriesAppliedFilterIsDisplayed =
    displayedSubCategories.length !== 0;
  const brandsAppliedFilterIsDisplayed =
    !joinedPageData.brand && displayedBrands.length !== 0;

  const clearButtonIsDisplayed =
    searchAppliedFilterIsDisplayed ||
    categoriesAppliedFilterIsDisplayed ||
    subCategoriesAppliedFilterIsDisplayed ||
    brandsAppliedFilterIsDisplayed;

  return (
    <div className="regularL:max-w-[70%] flex flex-wrap mx-4 gap-3">
      {searchAppliedFilterIsDisplayed && (
        <AppliedFilter type={"search"} item={{ id: "", name: search }} />
      )}
      {categoriesAppliedFilterIsDisplayed &&
        displayedcategories.map((category, idx) => (
          <AppliedFilter key={idx} type={"category"} item={category} />
        ))}
      {subCategoriesAppliedFilterIsDisplayed &&
        displayedSubCategories.map((subCategory, idx) => (
          <AppliedFilter key={idx} type={"subCategory"} item={subCategory} />
        ))}
      {brandsAppliedFilterIsDisplayed &&
        displayedBrands.map((brand, idx) => (
          <AppliedFilter key={idx} type={"brand"} item={brand} />
        ))}
      {clearButtonIsDisplayed && (
        <Button
          variant="ghost"
          className="underline hover:no-underline"
          onClick={clearAll}
        >
          {t.raw("clearAll")}
        </Button>
      )}
    </div>
  );
}
