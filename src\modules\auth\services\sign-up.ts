import { POST } from "@/lib/http-methods";
import { UserSignUpType } from "@/modules/auth/types";
import { castToSignUpServerSideType } from "@/modules/auth/utils/data-utils/types-casting/user";
import { CustomError } from "@/utils/custom-error";
import { AxiosError } from "axios";

export async function signUp(data: UserSignUpType) {
  try {
    await POST(`/auths/register`, {}, castToSignUpServerSideType(data));
  } catch (error) {
    const axiosError = error as AxiosError<{ code: string; message: string }>;

    const responseStatus = axiosError.response?.status ?? 500;
    const responseCode = axiosError.response?.data.code;
    const errorMessage =
      axiosError.response?.data.message ||
      axiosError.message ||
      "Unknown error";

    throw new CustomError(errorMessage, responseStatus, responseCode);
  }
}
