import { castToMetaContentType } from "@/utils/types-casting/meta-content";
import {
  ItemInResponseType,
  ItemType,
  PriceRange,
  PriceRangeInResponse,
  ProductInResponseType,
  ProductType,
} from "../../types/products";

export function castToProductType(
  productInResponse: ProductInResponseType
): ProductType {
  return {
    metaContent: castToMetaContentType(productInResponse.metaContent),
    slug: productInResponse.slug,
    brand: productInResponse.brand
      ? {
          slug: productInResponse.brand.slug,
          name: productInResponse.brand.name,
          image: productInResponse.brand.image
            ? `${process.env.BACKEND_ADDRESS}${productInResponse.brand.image}`
            : undefined,
        }
      : null,
    categoryIds: productInResponse.categoryIds,
    name: productInResponse.name as string,
    description: productInResponse.description as string,
    details: productInResponse.details,
    id: productInResponse.id,
    items: productInResponse.items.map((item) => castToItemType(item)),
  };
}

function castToItemType(item: ItemInResponseType): ItemType {
  return {
    id: item.id,
    reference: item.reference,
    barcode: item.barcode,
    image:
      item.image && item.image != "null"
        ? `${process.env.BACKEND_ADDRESS}${item.image}`
        : "/not-found/product-image.webp",
    images: item.images.map((image) =>
      image && image != "null"
        ? `${process.env.BACKEND_ADDRESS}${image}`
        : "/not-found/product-image.webp"
    ),
    variations: item.variation || [],
    prices: item.prices.map((price) => {
      return {
        promotionalPrice: Number(price.promotionalPrice),
        realPrice: Number(price.regularPrice),
        currency: price.currency,
      };
    }),
    inStock: item.inStock,
    promotion: item.promotion,
  };
}

export function castToPriceRange(
  priceRangeInResponse: PriceRangeInResponse
): PriceRange {
  return {
    minPrice: Number(priceRangeInResponse.min),
    maxPrice: Number(priceRangeInResponse.max),
  };
}
