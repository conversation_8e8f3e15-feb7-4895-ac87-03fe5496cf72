import { GET } from "@/lib/http-methods";
import { BrandInResponseType } from "../../types/brands";
import { castToBrandType } from "../../utils/types-casting/brands";
import { getBackendLocaleOnParams } from "@/utils/backend-locale";

interface Params {
  slug: string;
  locale?: string;
}

export async function retrieveBrandFromServerSide({ slug, locale }: Params) {
  const params = [];

  if (locale) params.push(getBackendLocaleOnParams({ locale }));

  try {
    const res = await GET(`/brands/${slug}?${params.join("&")}`, {});

    return castToBrandType(res.data as BrandInResponseType);
  } catch (error) {
    return null;
  }
}
