import { keepPreviousData, useQuery } from "@tanstack/react-query";
import { retrieveCategoryFromServerSide } from "../../services/categories/category-extraction";
import { CategoryType } from "../../types/categories";
import useBackendLocale from "@/hooks/use-backend-locale";

interface Params {
  categorySlug: string;
}

export default function useCategory({ categorySlug }: Params) {
  const { backendLocale } = useBackendLocale();
  const { data, isLoading } = useQuery<CategoryType | null>({
    queryKey: ["category", categorySlug, backendLocale],
    queryFn: () =>
      retrieveCategoryFromServerSide({
        slug: categorySlug,
        locale: backendLocale,
      }),
    placeholderData: keepPreviousData,
  });

  return {
    categoryIsLoading: isLoading,
    category: data,
  };
}
