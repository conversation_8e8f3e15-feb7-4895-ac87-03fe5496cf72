import { castToMetaContentType } from "@/utils/types-casting/meta-content";
import { BrandInResponseType, BrandType } from "../../types/brands";

export function castToBrandType(
  brandInResponse: BrandInResponseType
): BrandType {
  return {
    metaContent: castToMetaContentType(brandInResponse.metaContent),
    slug: brandInResponse.slug,
    id: brandInResponse.id,
    name: brandInResponse.name,
    image: brandInResponse.image
      ? `${process.env.BACKEND_ADDRESS}${brandInResponse.image}`
      : `/not-found/product-image.webp`,
    numberOfProducts: Number(brandInResponse.numberOfProducts),
  };
}
