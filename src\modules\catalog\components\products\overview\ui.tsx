"use client";
import { But<PERSON> } from "@/components/ui/button";
import ProductContainer from "../product/container/default";
import { type HTMLAttributes } from "react";
import { cn } from "@/lib/utils";
import Link from "next/link";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import { ProductType } from "@/modules/catalog/types/products";
import { useWindowWidth } from "@react-hook/window-size";

interface Props extends HTMLAttributes<"html"> {
  title?: string;
  subtitle?: string;
  products?: ProductType[];
  isLoading: boolean;
  checkMoreButton?: { content: string; link: string };
  bannerImage?: { src: string; alt: string };
  selectors?: { name: string; onClick: () => void; id: string }[];
  selectedSelectorId?: string;
}

export default function ProductsOverviewUI({
  title,
  subtitle,
  products,
  checkMoreButton,
  isLoading,
  selectors = [],
  selectedSelectorId,
}: Props) {
  const screenWidth = useWindowWidth();

  return (
    <div className={cn("w-full border flex flex-col items-center space-y-8")}>
      {/* Header Section */}
      {(title || subtitle) && products && products.length > 0 && (
        <div className="text-center space-y-2">
          {title && (
            <h2 className="text-2xl font-bold tracking-tight">{title}</h2>
          )}
          {subtitle && (
            <p className="text-lg text-muted-foreground max-w-2xl">
              {subtitle}
            </p>
          )}
        </div>
      )}

      {/* Category Filter Buttons */}
      {selectors.length > 0 && (
        <div className="w-full">
          <div className="flex flex-wrap gap-2 justify-center">
            {selectors.map((selector) => (
              <Button
                key={selector.id}
                onClick={selector.onClick}
                variant={
                  selector.id === selectedSelectorId ? "default" : "outline"
                }
                size="sm"
                className="rounded-full"
              >
                {selector.name}
              </Button>
            ))}
          </div>
        </div>
      )}

      {/* Products Section */}
      {isLoading || products === undefined ? (
        <ProductsOverviewSkeletons />
      ) : (
        products &&
        products.length > 0 && (
          <div className="w-full space-y-6">
            <Carousel
              opts={{ loop: true }}
              className="w-full flex gap-2 items-center"
            >
              <CarouselPrevious className="static" />
              <CarouselContent className="">
                {products?.map((product) => (
                  <CarouselItem key={product.id} className="max-w-2xs">
                    <ProductContainer product={product} />
                  </CarouselItem>
                ))}
              </CarouselContent>
              <CarouselNext className="static" />
            </Carousel>

            {checkMoreButton && (
              <div className="flex justify-center">
                <Button asChild>
                  <Link href={checkMoreButton.link}>
                    {checkMoreButton.content}
                  </Link>
                </Button>
              </div>
            )}
          </div>
        )
      )}
    </div>
  );
}

export function ProductsOverviewSkeletons({
  className,
}: {
  className?: string;
}) {
  return (
    <div
      className={cn("w-full flex flex-col items-center space-y-8", className)}
    >
      <div className="w-full grid regularL:grid-cols-4 grid-cols-2 gap-[10px]">
        {Array.from({ length: 4 }).map((_, idx) => (
          <ProductContainer key={idx} product={null} />
        ))}
      </div>
      <Skeleton className="h-10 w-40 rounded-full" />
    </div>
  );
}
