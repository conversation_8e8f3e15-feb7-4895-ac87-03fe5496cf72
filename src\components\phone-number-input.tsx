import { cn } from "@/lib/utils";
import { HTMLAttributes } from "react";

interface PhoneNumberPropsType extends HTMLAttributes<HTMLDivElement> {
  code: string;
  inputClassName?: string;
  inputName: string;
  primaryTheme?: boolean;
}

export default function PhoneNumberInput({
  code,
  className,
  inputName,
  inputClassName,
  primaryTheme = true,
}: PhoneNumberPropsType) {
  return (
    <div
      className={cn(
        "h-10 pl-3 flex items-center bg-opacity-55 border border-gray rounded-[10px]",
        className,
        {
          "border-green": primaryTheme,
        }
      )}
      dir="ltr"
    >
      <p
        className={cn("border-gray pr-3 border-r", inputClassName, {
          "border-green": primaryTheme,
        })}
      >
        {code}
      </p>
      <input
        name={inputName}
        className={cn("px-2 flex-1 h-full bg-transparent outline-none")}
      />
    </div>
  );
}
