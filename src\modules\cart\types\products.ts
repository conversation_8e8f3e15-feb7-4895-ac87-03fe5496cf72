import { VariationType } from "@/modules/catalog/types/products";

export interface ProductItemType {
  id: string;
  slug: string;
  name: string;
  productId: string;
  productItemCartId?: string;
  image: string;
  prices: PriceType[];
  cartQuantity: number;
  variations: VariationType[];
}

export interface ProductInResponse {
  id: string;
  slug: string;
  quantity: number;
  productItem: ProductItemInResponseType;
}

export interface ProductItemInResponseType {
  id: string;
  productId: string;
  barcode: string;
  reference: string;
  name: string;
  image: string | null;
  images?: string[];
  prices: PriceInResponseType[];
  inStock: boolean;
}

export interface PriceType {
  currency: string;
  realPrice: number;
  promotionalPrice: number;
}

export interface PriceInResponseType {
  currency: string;
  regularPrice: number;
  promotionalPrice: number;
}

export interface PostedCartItemType {
  quantity: number;
  id: string;
}
