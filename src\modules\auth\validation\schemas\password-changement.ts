import { z } from "zod";
import { TranslateFunction } from "@/types";

const MIN_PASSWORD_LENGTH = 8;

export function getPasswordChangementSchema(t?: TranslateFunction) {
  return z
    .object({
      currentPassword: z.string().min(MIN_PASSWORD_LENGTH, {
        message: t
          ? t("password.tooShort", { min: MIN_PASSWORD_LENGTH })
          : `Le mot de passe doit contenir au moins ${MIN_PASSWORD_LENGTH} caractères.`,
      }),
      newPassword: z.string().min(MIN_PASSWORD_LENGTH, {
        message: t
          ? t("password.tooShort", { min: MIN_PASSWORD_LENGTH })
          : `Le mot de passe doit contenir au moins ${MIN_PASSWORD_LENGTH} caractères.`,
      }),
      confirmationPassword: z.string().min(MIN_PASSWORD_LENGTH, {
        message: t
          ? t("password.tooShort", { min: MIN_PASSWORD_LENGTH })
          : `Le mot de passe doit contenir au moins ${MIN_PASSWORD_LENGTH} caractères.`,
      }),
    })
    .refine((data) => data.newPassword === data.confirmationPassword, {
      message: t
        ? t("password.mismatch")
        : "Les mots de passe ne correspondent pas.",
      path: ["confirmationPassword"],
    });
}
