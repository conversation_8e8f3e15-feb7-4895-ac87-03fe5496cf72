import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import useOTPSubmission from "../../hooks/reset-password/use-otp-submission";
import { useTranslations } from "next-intl";

interface Props {
  email: string;
  onSuccess: () => void;
  saveCode: (code: string) => void;
}

export default function OtpSubmission({ email, onSuccess, saveCode }: Props) {
  const t = useTranslations("modules.auth.resetPassword");

  const { error, onSubmit, isPending, form } = useOTPSubmission({
    email,
    saveCode,
    onSuccess,
  });

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="flex flex-col gap-6"
      >
        <div className="flex flex-col items-center gap-2 text-center">
          <h1 className="text-2xl font-bold">{t("steps.otp.title")}</h1>
          <p className="text-muted-foreground text-sm text-balance">
            {t("steps.otp.description")}
          </p>
        </div>
        <div className="grid gap-6">
          <FormField
            control={form.control}
            name="otp"
            render={({ field }) => (
              <FormItem>
                <FormLabel htmlFor="otp">{t("steps.otp.label")}</FormLabel>
                <FormControl>
                  <Input
                    id="otp"
                    placeholder={t("steps.otp.placeholder")}
                    {...field}
                    maxLength={6}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          {error && (
            <p className="text-sm text-destructive text-center">{error}</p>
          )}
          <Button type="submit" className="w-full" disabled={isPending}>
            {isPending
              ? t("steps.otp.button.loading")
              : t("steps.otp.button.default")}
          </Button>
        </div>
      </form>
    </Form>
  );
}
