"use client";

import { Input } from "../ui/input";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { useTranslations } from "next-intl";
import useEmailUpload from "@/hooks/news-letter/use-news-letter";
import { Button } from "../ui/button";

export default function NewsLetter() {
  const t = useTranslations("layout.newsLetter");

  const { onSubmit, form, warning, isPending, isSuccess, isError } =
    useEmailUpload();

  return (
    <div className="col-span-2">
      <h6 className="font-semibold">{t("title")}</h6>
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="mt-6 flex gap-2"
        >
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem className="grow">
                <FormLabel className="sr-only">
                  {t("input.email.label")}
                </FormLabel>
                <FormControl>
                  <Input
                    placeholder={t("input.email.placeholder")}
                    className="grow flex-1 max-w-64"
                    {...field}
                  />
                </FormControl>
                {warning === "" && <FormMessage />}
              </FormItem>
            )}
          />
          <Button type="submit" disabled={isPending}>
            {isPending ? t("submission.isPending") : t("submission.submit")}
          </Button>
        </form>
      </Form>

      {isSuccess && (
        <p className="text-green-600 text-sm mt-2">{t("successMessage")}</p>
      )}
      {isError && warning && (
        <p className="text-destructive text-sm mt-2">{warning}</p>
      )}
    </div>
  );
}
