import { Button } from "@/components/ui/button";
import { useCartStore } from "@/modules/cart/store/cart-store";
import { ProductItemType } from "@/modules/cart/types/products";
import Text from "@/styles/text-styles";
import { Check, Minus, Plus } from "lucide-react";
import { useTranslations } from "next-intl";
import { useState } from "react";
import { useRouter } from "next/navigation";
import { AuthDialog } from "@/modules/auth/components/auth-dialog";
import { cn } from "@/lib/utils";
import { useAuthDialogState } from "@/modules/auth/store/auth-dialog-state-store";
import { useAuthMode } from "@/modules/auth/store/auth-mode-store";
import usePricePointsConverter from "@/modules/points-system/hook/use-price-points-converter";
import useUserStore from "@/modules/auth/store/user-store";

interface Props {
  productItem: ProductItemType | null;
}

export default function ProductPurchaseConfigurator({ productItem }: Props) {
  const router = useRouter();
  const { user } = useUserStore((store) => store);
  const [quantity, setQuantity] = useState(1);
  const { setIsOpen } = useAuthDialogState((store) => store);
  const t = useTranslations("productPage");
  const { addProductItem } = useCartStore((store) => store.actions);
  const { setMode } = useAuthMode();
  const { points: pointsToWin, pointsPrice } = usePricePointsConverter(
    productItem?.prices[0].promotionalPrice || 0
  );

  return productItem ? (
    <div className="L:space-y-6 space-y-4">
      {/* Purchase Options */}
      {
        <div className="L:space-y-4 space-y-2">
          <label>
            <Text textStyle="TS6" className="text-primary font-bold">
              {t("purchase.type")}
            </Text>
          </label>
          <div className="space-y-3">
            <Button
              disabled={user !== null && user.isAuthenticated}
              onClick={() => {
                addProductItem(
                  {
                    slug: productItem.slug,
                    id: productItem.id,
                    cartQuantity: quantity,
                    productId: productItem.productId,
                    name: productItem.name,
                    prices: productItem.prices,
                    image: productItem.image,
                    variations: productItem.variations,
                  },
                  false
                );

                router.push("/paiement");
              }}
              className={cn(
                "w-full border rounded-xl border-primary bg-primary text-white hover:bg-white hover:text-primary font-bold",
                {
                  "bg-white text-primary":
                    user !== null && user.isAuthenticated,
                }
              )}
            >
              <Text textStyle="TS7">{t("purchase.withoutAccount")}</Text>
            </Button>

            <div
              className={cn(
                "px-4 py-2 rounded-xl flex  gap-4 justify-between border border-primary",
                {
                  "3L:flex-row flex-col 3L:items-center": !(
                    user !== null && user.isAuthenticated
                  ),
                  "items-center bg-primary text-white":
                    user !== null && user.isAuthenticated,
                }
              )}
            >
              <div className="flex flex-col space-y-1">
                <Text textStyle="TS7" className="font-bold">
                  {t("purchase.withAccount")}
                </Text>
                <Text
                  textStyle="TS8"
                  className={cn("text-secondary", {
                    "text-white": user !== null && user.isAuthenticated,
                  })}
                >
                  {t.rich("achatPoints", {
                    points: (points) => (
                      <span>{`${pointsToWin.toFixed(3)} ${points}`}</span>
                    ),
                    amount: (amount) => (
                      <span>{`${pointsPrice} ${amount} ${productItem.prices[0].currency}`}</span>
                    ),
                  })}
                </Text>
              </div>

              {!(user && user.isAuthenticated) ? (
                <Button
                  className={
                    "rounded-lg border border-primary bg-primary text-white hover:bg-white hover:text-primary"
                  }
                  onClick={() => {
                    setMode("signUp");
                    setIsOpen(true);
                  }}
                >
                  <Text textStyle="TS7">{t("purchase.joinOurFamily")}</Text>
                </Button>
              ) : (
                <Check />
              )}
            </div>
          </div>
        </div>
      }

      {/* Quantity and Add to Cart */}
      <div className="h-max flex items-stretch L:gap-4 gap-2">
        <div className="px-2 L:py-2 flex items-center border border-primary rounded-md">
          <Text
            textStyle="TS6"
            className={cn("text-center text-primary M:block hidden")}
          >
            {t("quantity")}
          </Text>

          <Button
            variant="ghost"
            size="icon"
            disabled={quantity <= 1}
            onClick={() => {
              setQuantity(quantity - 1);
            }}
          >
            <Minus className="w-4 h-4 text-primary" />
          </Button>

          <Text textStyle="TS6" className="text-center text-primary w-6">
            {quantity}
          </Text>

          <Button
            variant="ghost"
            size="icon"
            onClick={() => {
              setQuantity(quantity + 1);
            }}
          >
            <Plus className="w-4 h-4 text-primary" />
          </Button>
        </div>

        <Button
          variant={"default"}
          className="p-7 rounded-lg flex-1 flex items-center border bg-secondary hover:bg-white hover:text-secondary border-secondary"
          onClick={() => {
            addProductItem({
              slug: productItem.slug,
              id: productItem.id,
              productId: productItem.productId,
              cartQuantity: quantity,
              name: productItem.name,
              prices: productItem.prices,
              image: productItem.image,
              variations: productItem.variations,
            });
          }}
        >
          <Text textStyle="TS7">{t("addToCard")}</Text>
        </Button>
      </div>

      <div className="hidden">
        <AuthDialog />
      </div>
    </div>
  ) : null;
}
