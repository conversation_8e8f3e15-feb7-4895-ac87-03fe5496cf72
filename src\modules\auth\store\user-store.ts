import { create } from "zustand";
import { UserType } from "../types";

type Store = {
  isLoading: boolean;
  user: UserType | null;
  setUser: (user: UserType | null) => void;
  setIsLoading: (isLoading: boolean) => void;
};

const useUserStore = create<Store>((set) => ({
  isLoading: true,
  user: null,
  setUser: (user) =>
    set(() => ({
      user,
    })),
  setIsLoading: (isLoading) => set(() => ({ isLoading })),
}));

export default useUserStore;
