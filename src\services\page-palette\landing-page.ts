import { GET } from "@/lib/http-methods";
import { getBackendLocaleOnParams } from "@/utils/backend-locale";
import { castToLandingPageContentType } from "@/utils/types-casting/hero-section-images";

interface Params {
  locale: string;
}

export default async function getLandingPageContent({ locale }: Params) {
  try {
    const res = await GET(
      `/page-palette/landing-page?${getBackendLocaleOnParams({ locale })}`,
      {}
    );

    return castToLandingPageContentType(res.data);
  } catch (error) {
    return null;
  }
}
