"use client";
import { Skeleton } from "@/components/ui/skeleton";
import { useTranslations } from "next-intl";
import { useEffect } from "react";
import useBrands from "../../hooks/brands/use-brands";
import BrandContainer from "./brand-container";
import {
  Marque<PERSON>,
  MarqueeContent,
  MarqueeItem,
} from "@/components/ui/shadcn-io/marquee";

export default function BrandsOverview() {
  const t = useTranslations("modules.catalog.brands.overview");
  const { brands, brandsAreLoading, setPage, pagesNumber } = useBrands({
    limit: 10,
  });

  // brands animation addition
  useEffect(() => {
    let animationFrameId = 0;
    if (pagesNumber !== 1) {
      function setRandomPageNumber() {
        const randomPage = Math.floor(Math.random() * pagesNumber) + 1;
        setPage(randomPage === pagesNumber ? randomPage - 1 : randomPage);
        setTimeout(setRandomPageNumber, 40000);
      }
      animationFrameId = requestAnimationFrame(setRandomPageNumber);
    }
    return () => {
      cancelAnimationFrame(animationFrameId);
    };
  }, [pagesNumber, setPage]);

  return !brandsAreLoading && brands ? (
    <div className="w-full flex items-center justify-center">
      <div className="overflow-hidden">
        <p className="text-center text-xl font-medium">{t("title")}</p>
        <div className="mt-20 max-w-screen-lg space-y-8">
          <Marquee>
            <MarqueeContent
              pauseOnHover
              direction="right"
              className="[--duration:30s] [&_svg]:mr-10"
            >
              {brands.map((brand) => (
                <MarqueeItem key={brand.id}>
                  <BrandContainer brand={brand} />
                </MarqueeItem>
              ))}
            </MarqueeContent>
          </Marquee>
          <MarqueeContent
            pauseOnHover
            direction="left"
            className="[--duration:30s] [&_svg]:mr-10"
          >
            {brands.map((brand) => (
              <MarqueeItem key={brand.id}>
                <BrandContainer brand={brand} />
              </MarqueeItem>
            ))}
          </MarqueeContent>
        </div>
      </div>
    </div>
  ) : (
    <div className="relative flex w-full flex-col items-center space-y-9 overflow-hidden rounded-[33px] px-4 py-8 md:py-12">
      <Skeleton className="h-8 w-[250px]" />

      {/* Brands section */}
      <div className="grid w-full grid-cols-2 gap-4 md:gap-6 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5">
        {Array.from({ length: 10 }).map((_, idx) => (
          <Skeleton
            key={idx}
            className="h-[50px] w-[120px] lg:h-[80px] lg:w-[168px]"
          />
        ))}
      </div>
      <Skeleton className="h-10 w-[200px] rounded-lg" />
    </div>
  );
}
