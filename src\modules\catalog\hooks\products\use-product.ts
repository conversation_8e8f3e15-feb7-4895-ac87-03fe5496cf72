import { keepPreviousData, useQuery } from "@tanstack/react-query";
import { retrieveProductFromServerSide } from "../../services/products/product";
import { ItemType, ProductType } from "../../types/products";
import { useEffect, useState } from "react";
import { CustomError } from "@/utils/custom-error";
import useBackendLocale from "@/hooks/use-backend-locale";

export default function useProduct(slug: string) {
  const { backendLocale } = useBackendLocale();

  const { data, isLoading, error } = useQuery<ProductType | null, CustomError>({
    queryKey: ["product", slug, backendLocale],
    queryFn: () =>
      retrieveProductFromServerSide({ slug, locale: backendLocale }),
    placeholderData: keepPreviousData,
  });
  const [productItem, setProductItem] = useState<ItemType | null>(null);

  useEffect(() => {
    if (data) setProductItem(data.items[0]);
  }, [data]);

  return {
    productIsLoading: isLoading,
    product: data,
    productItem,
    setProductItem,
    error,
  };
}
