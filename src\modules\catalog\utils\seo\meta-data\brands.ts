import { metadata } from "@/app/[locale]/layout";
import { retrieveBrandFromServerSide } from "@/modules/catalog/services/brands/brand-extraction";
import { getBackendLocaleOnServerSide } from "@/utils/backend-locale";
import { AxiosError } from "axios";
import { Metadata } from "next";
import { notFound } from "next/navigation";

interface Params {
  brandSlug: string;
}

export async function generateBrandMetaData({
  brandSlug,
}: Params): Promise<Metadata> {
  try {
    const locale = await getBackendLocaleOnServerSide();
    const brand = await retrieveBrandFromServerSide({
      slug: brandSlug,
      locale,
    });

    if (brand?.metaContent) {
      return {
        title: brand.metaContent.title,
        description: brand.metaContent.description,
        keywords: brand.metaContent.keywords,
      };
    }

    return metadata;
  } catch (error) {
    const axiosError = error as AxiosError;

    if (axiosError?.response?.status === 404) notFound();

    return metadata;
  }

  return metadata;
}
