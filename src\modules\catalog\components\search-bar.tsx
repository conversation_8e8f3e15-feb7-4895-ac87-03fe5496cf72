import { Search, X } from "lucide-react";
import { useTranslations } from "next-intl";
import {
  Sheet,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  SheetDescription,
  She<PERSON><PERSON><PERSON><PERSON>,
  She<PERSON><PERSON><PERSON><PERSON>,
  Sheet<PERSON>rigger,
} from "@/components/ui/sheet";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import Text, { TextStyle } from "@/styles/text-styles";
import SearchProductContainer from "./products/product/container/search";
import { cn } from "@/lib/utils";
import { useState } from "react";
import { Button } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import { Separator } from "@/components/ui/separator";
import useScreenSize from "@/hooks/use-screen-size";
import { KeyboardEvent } from "react";
import useProducts from "../hooks/products/use-products";

export default function SearchBar() {
  const t = useTranslations("shared.navbar.appHeader.searchBar");
  const [searchIsOpen, setSearchIsOpen] = useState(false);
  const [search, setSearch] = useState("");
  const { width } = useScreenSize();
  const MScreen = 450 < width;
  const router = useRouter();
  const { products, productsAreLoading } = useProducts({
    limit: 8,
    search: search.trim(),
  });

  const handleSearchSubmit = () => {
    setSearchIsOpen(false);
    router.push(`/produits/filtres?search=${search}`);
  };

  const handleKeyDown = (event: KeyboardEvent<HTMLInputElement>) => {
    if (event.key === "Enter" && search.trim() !== "") {
      handleSearchSubmit();
    }
  };

  return (
    <div className="bg-white regularL:relative">
      <Sheet open={searchIsOpen} onOpenChange={(open) => setSearchIsOpen(open)}>
        <SheetTrigger asChild>
          <button className="mt-2 L:mt-0">
            <Search size={18} className="text-primary" />
          </button>
        </SheetTrigger>
        <SheetContent
          side={MScreen ? "right" : "top"}
          aria-label="bar de recherche"
          className={cn(
            "L:p-4 p-2 border border-primary rounded-[15px] top-5 M:max-w-sm L:max-w-[500px] right-5 M:left-auto left-5",
            {
              "h-[calc(100%-40px)]":
                (products !== undefined && products.length > 0) ||
                MScreen ||
                productsAreLoading,
            }
          )}
        >
          <SheetHeader>
            <SheetTitle></SheetTitle>
            <SheetDescription></SheetDescription>
          </SheetHeader>
          <div className="w-full h-full flex-1 flex flex-col">
            <div
              id="searchbar"
              className={cn("w-full regularL:px-0 px-2 bg-white duration-500")}
            >
              <div className="group bg-white w-full pb-1 border-b-2 border-primary flex items-center space-x-2 px-2">
                <Input
                  placeholder={t("placeholder")}
                  className={cn(
                    "h-fit outline-none border-none font-swell text-primary placeholder:text-primary/40",
                    TextStyle["TS2"]
                  )}
                  value={search}
                  onChange={(e) => setSearch(e.target.value)}
                  onKeyDown={handleKeyDown}
                />
                <div className="flex items-center space-x-2">
                  <Button
                    variant="ghost"
                    className="px-0 w-[34px] h-[34px] border border-primary rounded-full"
                    onClick={() => setSearchIsOpen(false)}
                  >
                    <X className={cn("text-primary", TextStyle["TS2"])} />
                  </Button>
                </div>
              </div>
            </div>
            {search.trim() !== "" ? (
              <ScrollArea className="flex-1">
                <div className="w-full py-2 flex flex-col space-y-3">
                  <Text
                    key={"results-label"}
                    textStyle="TS3"
                    className="text-primary"
                  >
                    {t("resultsLabel")}
                  </Text>
                  {productsAreLoading ? (
                    Array.from({ length: 7 }).map((_, idx) => (
                      <SearchProductContainer key={idx} product={null} />
                    ))
                  ) : products && products.length > 0 ? (
                    products.map((product) => (
                      <SearchProductContainer
                        key={product.id}
                        product={product}
                        onClick={() => {
                          setSearchIsOpen(false);
                        }}
                      />
                    ))
                  ) : (
                    <Text
                      textStyle="TS5"
                      className="text-gray font-bold min-h-[300px] flex justify-center items-center"
                    >
                      {t("noResults")}
                    </Text>
                  )}
                  <Separator />
                  {search.trim() !== "" &&
                    !productsAreLoading &&
                    products &&
                    products.length !== 0 && (
                      <div className="w-full flex justify-center">
                        <Button
                          variant="link"
                          className="py-2 px-0 self-center before:bg-primary flex space-x-2"
                          onClick={handleSearchSubmit}
                        >
                          <Text textStyle="TS5" className="text-primary">
                            {t("seeMore")}
                          </Text>
                          <Search size={20} className="text-primary" />
                        </Button>
                      </div>
                    )}
                </div>
              </ScrollArea>
            ) : null}
          </div>
        </SheetContent>
      </Sheet>
    </div>
  );
}
