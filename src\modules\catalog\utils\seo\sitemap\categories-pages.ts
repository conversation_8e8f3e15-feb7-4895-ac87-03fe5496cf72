import { MetadataRoute } from "next";
import { retrieveCategoriesFromServerSide } from "../../../services/categories/categories-extraction";
import { getBackendLocaleOnServerSide } from "@/utils/backend-locale";

export async function getCategoriesPages(): Promise<MetadataRoute.Sitemap> {
  try {
    const locale = await getBackendLocaleOnServerSide();
    const categories = await retrieveCategoriesFromServerSide({ locale });

    if (categories) {
      const categoriesPages: MetadataRoute.Sitemap = categories.flatMap(
        (category) =>
          [
            {
              url: `https://${process.env.FRONTEND_DOMAIN_NAME}/${category.slug}`,
              lastModified: new Date(),
              changeFrequency: "monthly",
              priority: 1,
            },
            ...category.subCategories.flatMap((subCategory) => [
              {
                url: `https://${process.env.FRONTEND_DOMAIN_NAME}/${category.slug}/${subCategory.slug}`,
                lastModified: new Date(),
                changeFrequency: "monthly",
                priority: 1,
              },
              ...subCategory.subCategories.map((subSubCategory) => ({
                url: `https://${process.env.FRONTEND_DOMAIN_NAME}/${category.slug}/${subCategory.slug}/${subSubCategory.slug}`,
                lastModified: new Date(),
                changeFrequency: "monthly",
                priority: 1,
              })),
            ]),
          ] as MetadataRoute.Sitemap
      );

      return categoriesPages;
    }
  } catch {
    return [];
  }

  return [];
}
