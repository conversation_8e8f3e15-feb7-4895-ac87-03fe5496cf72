"use client";

import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/lib/utils";
import { User } from "lucide-react";
import { useTranslations } from "next-intl";
import useUserStore from "@/modules/auth/store/user-store";
import { logger } from "@/utils/logger";

export default function UserWelcome() {
  const { user, isLoading } = useUserStore((store) => store);
  const t = useTranslations("accountPage.welcoming");
  logger.log(user);
  return !isLoading ? (
    <div className="w-full flex flex-col space-y-10 justify-center items-center text-primary">
      <div className="w-[116px] h-[116px] bg-primary/20 rounded-full flex justify-center items-center">
        <div className="w-[96px] h-[96px] bg-primary/50 rounded-full flex justify-center items-center">
          <div className="w-[76px] h-[76px] bg-primary rounded-full flex justify-center items-center text-white">
            <User className="w-6 h-6" />{" "}
            {/* Adjusted size for lucide-react icon */}
          </div>
        </div>
      </div>
      <div
        className={cn("md:w-[80%] flex flex-col items-center space-y-9", {})}
      >
        <h2 className="text-3xl font-bold text-center">
          {t.rich("title", {
            name: (chunk) => <span className="text-primary">{user?.name}</span>,
          })}
        </h2>
        <p className="text-base text-center text-muted-foreground">
          {t("description")}
        </p>
      </div>
    </div>
  ) : (
    <div className="px-2 w-full flex flex-col space-y-10 justify-center items-center text-primary">
      <Skeleton className="w-[116px] h-[116px] rounded-full" />
      <div
        className={cn(
          "md:w-[80%] w-full flex flex-col items-center space-y-9",
          {}
        )}
      >
        <Skeleton className="w-32 h-10" />
        <div className="sm:w-72 w-full flex flex-col space-y-1">
          {Array.from({ length: 4 }).map((_, idx) => (
            <Skeleton key={idx} className="w-full h-10" />
          ))}
        </div>
      </div>
    </div>
  );
}
