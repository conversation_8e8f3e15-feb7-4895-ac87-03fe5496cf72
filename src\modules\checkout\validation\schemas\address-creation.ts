import { TranslateFunction } from "@/types";
import { Country } from "@shopify/address";
import parsePhoneNumberFromString from "libphonenumber-js";
import z from "zod";

interface Params {
  t?: TranslateFunction;
  country: Country | null;
}

export function getAddressCreationFormSchema({ t, country }: Params) {
  return z.object({
    email: z
      .email(t ? t("email.invalid") : "L'adresse e-mail est invalide.")
      .min(1, {
        message: t ? t("email.required") : "L'adresse e-mail est requise.",
      }),
    firstName: z.string().min(1, {
      message: t ? t("firstName.required") : "Le prénom est requis.",
    }),
    lastName: z.string().min(1, {
      message: t ? t("lastName.required") : "Le nom de famille est requis.",
    }),
    address1: z.string().min(1, {
      message: t ? t("address1.required") : "L'adresse est requise.",
    }),
    address2: z.string().optional(),
    company: z.string().optional(),
    province: z.string().optional(),
    zip: z.string().optional(),
    phone: z
      .string()
      .min(1, {
        message: t ? t("phone.required") : "Le numéro de téléphone est requis.",
      })
      .refine(
        (value) => {
          if (country) {
            const phoneNumber = `+${country.phoneNumberPrefix}${value}`;
            return parsePhoneNumberFromString(phoneNumber)?.isValid() || false;
          }
          // if country null
          return true;
        },
        {
          message: t ? t("phone.invalid") : "Numéro de téléphone invalide.",
        }
      ),
    city: z.string().min(1, {
      message: t ? t("city.required") : "La ville est requise.",
    }),
  });
}
