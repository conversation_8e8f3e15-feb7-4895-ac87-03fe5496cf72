"use client";

import type React from "react";
import { cn } from "@/lib/utils";
import useResetPassword from "@/modules/auth/hooks/reset-password";
import EmailStep from "./email-submission";
import OtpStep from "./otp-submission";
import NewPasswordSubmission from "./new-password-submission";
import SuccessfulResetPassword from "./success";

export default function ResetPasswordForm({
  className,
  ...props
}: React.ComponentProps<"div">) {
  const { email, setEmail, code, setCode, step, setStep } = useResetPassword();

  return (
    <div className={cn("flex flex-col gap-6", className)} {...props}>
      {step === "email" && (
        <EmailStep saveEmail={setEmail} onSuccess={() => setStep("otp")} />
      )}
      {step === "otp" && (
        <OtpStep
          email={email}
          saveCode={setCode}
          onSuccess={() => setStep("password")}
        />
      )}
      {step === "password" && (
        <NewPasswordSubmission
          email={email}
          code={code}
          onSuccess={() => setStep("password")}
        />
      )}
      {step === "success" && <SuccessfulResetPassword />}
    </div>
  );
}
