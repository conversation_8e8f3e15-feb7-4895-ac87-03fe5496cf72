import { useEffect } from "react";
import usePagination from "@/hooks/use-pagination";
import { keepPreviousData, useQuery } from "@tanstack/react-query";
import { ProductType } from "../../types/products";
import { PaginationType } from "@/types";
import { retrievePromotionProductsFromServerSide } from "../../services/promotions/products-extraction";
import { CustomError } from "@/utils/custom-error";
import useBackendLocale from "@/hooks/use-backend-locale";

interface Params {
  limit: number;
  promotionSlug: string;
  queryKeys?: (string | number)[];
}
export default function usePromotionProducts({
  limit,
  promotionSlug,
  queryKeys = [],
}: Params) {
  const { backendLocale } = useBackendLocale();

  const { page, setPage, pagesNumber, setPagesNumber, paginatedListRef } =
    usePagination();

  const { data, isLoading, error } = useQuery<
    {
      products: ProductType[];
      pagination: PaginationType;
    } | null,
    CustomError
  >({
    queryKey: [
      "promotion-products",
      page,
      promotionSlug,
      backendLocale,
      ...queryKeys,
    ],
    queryFn: () =>
      retrievePromotionProductsFromServerSide({
        page,
        limit,
        slug: promotionSlug,
        locale: backendLocale,
      }),
    placeholderData: keepPreviousData,
  });

  useEffect(() => {
    setPagesNumber(
      data?.pagination?.totalPages ? data?.pagination?.totalPages : 1
    );
  }, [data]);

  return {
    products: data?.products,
    productsAreLoading: isLoading,
    error,
    setPage,
    page,
    pagesNumber,
    paginatedListRef,
  };
}
