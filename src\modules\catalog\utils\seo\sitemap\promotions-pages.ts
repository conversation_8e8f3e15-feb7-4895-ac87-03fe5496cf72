import { MetadataRoute } from "next";
import retrievePromotionsFromServerSide from "../../../services/promotions/promotions-extraction";
import { getBackendLocaleOnServerSide } from "@/utils/backend-locale";

export async function getPromotionsPages(): Promise<MetadataRoute.Sitemap> {
  try {
    const locale = await getBackendLocaleOnServerSide();
    const promotions = await retrievePromotionsFromServerSide({ locale });

    if (promotions) {
      const promotionsPages: MetadataRoute.Sitemap = promotions.map(
        (promotion) => ({
          url: `https://${process.env.FRONTEND_DOMAIN_NAME}/promotions/${promotion.slug}`,
          lastModified: new Date(),
          changeFrequency: "weekly",
          priority: 1,
        })
      );

      return promotionsPages;
    }
  } catch {
    return [];
  }

  return [];
}
