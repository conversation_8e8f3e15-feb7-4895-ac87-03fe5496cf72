import { CarouselApi } from "@/components/ui/carousel";
import { useEffect, useState } from "react";

interface Params {
  autoChangement?: boolean;
  timer?: number;
}

export default function useCarouselIndicator(params?: Params) {
  const timer = params && params.timer ? params.timer : 10000;
  const [api, setApi] = useState<CarouselApi>();
  const [current, setCurrent] = useState(1);
  const [count, setCount] = useState(0);

  useEffect(() => {
    if (!api) {
      return;
    }

    setCount(api.scrollSnapList().length);
    setCurrent(api.selectedScrollSnap() + 1);

    api.on("select", () => {
      setCurrent(api.selectedScrollSnap() + 1);
    });
  }, [api]);

  useEffect(() => {
    let animationFrameId = 0;

    if (params?.autoChangement && count > 1 && api) {
      function setDisplayedHero() {
        setTimeout(() => {
          if (api) api.scrollNext();

          animationFrameId = requestAnimationFrame(setDisplayedHero);
        }, timer);
      }

      animationFrameId = requestAnimationFrame(setDisplayedHero);
    }

    return () => {
      cancelAnimationFrame(animationFrameId);
    };
  }, [count, api]);

  return { setApi, current, count, api, setCurrent };
}
