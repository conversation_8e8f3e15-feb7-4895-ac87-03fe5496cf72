import { useState } from "react";
import { useTranslations } from "next-intl";
import { useQueryClient } from "@tanstack/react-query";
import { useForm } from "react-hook-form";
import z from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { CustomError } from "@/utils/custom-error";
import { getAddressCreationFormSchema } from "../../validation/schemas/address-creation";
import createAddressOnServerSide from "../../services/addresses/address-creation";
import useAddressSelection from "../../store/address-selection-store";
import useCountries from "./use-countries";
import useCountryCities from "./use-country-cities";
import { useRouter } from "next/navigation";
import { STATIC_URLS } from "@/utils/urls";

interface UseAddressCreationFormParams {
  useAddressIdInCheckout?: boolean;
  onSuccess?: () => void;
}

export default function useAddressCreationForm({
  useAddressIdInCheckout = false,
  onSuccess,
}: UseAddressCreationFormParams = {}) {
  const queryClient = useQueryClient();
  const router = useRouter();
  const t = useTranslations("modules.checkout.validations");
  const errorsContent = useTranslations("modules.checkout.errors");

  const [isPending, setIsPending] = useState(false);
  const [error, setError] = useState("");

  const { country, countryOptionnalLabels } = useCountries();
  const { city } = useCountryCities({
    countryCode: country?.code as string,
  });

  const selectAddressForCheckout = useAddressSelection(
    (store) => store.selectAddress
  );

  const formSchema = getAddressCreationFormSchema({ t, country });
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: "",
      firstName: "",
      lastName: "",
      address1: "",
      address2: "",
      company: "",
      province: "",
      zip: "",
      phone: "",
      city: "",
    },
  });

  const exctractInfoForGuestCheckout = () => {
    const addressData = form.getValues();

    const addressRecord: Record<string, string> = {
      email: addressData.email,
      firstName: addressData.firstName,
      lastName: addressData.lastName,
      address1: addressData.address1,
      address2: addressData.address2 || "",
      company: addressData.company || "",
      province: addressData.province || "",
      zip: addressData.zip || "",
      phone: country
        ? `+${country.phoneNumberPrefix}${addressData.phone}`
        : addressData.phone,
      cityId: addressData.city,
    };

    return addressRecord;
  };

  async function onSubmit(values: z.infer<typeof formSchema>) {
    if (error !== "") setError("");
    setIsPending(true);

    const addressData = {
      email: values.email,
      firstName: values.firstName,
      lastName: values.lastName,
      address1: values.address1,
      address2: values.address2 || "",
      company: values.company || "",
      province: values.province || "",
      zip: values.zip || "",
      phone: values.phone,
      cityId: country?.code === "TN" ? city.code : values.city,
    };

    try {
      const res = await createAddressOnServerSide(addressData);

      queryClient.invalidateQueries({
        queryKey: ["user-address"],
        exact: false,
      });

      form.reset();

      if (useAddressIdInCheckout && res.address) {
        selectAddressForCheckout(res.address.id);
      }

      if (onSuccess) {
        onSuccess();
      }

      if (!useAddressIdInCheckout) {
        document.body.scrollIntoView({ behavior: "smooth" });
      }
    } catch (e) {
      const error = e as CustomError;

      if (error.status === 401) router.push(STATIC_URLS["auth"].signIn);
      else if (error.status === 400) {
        setError(errorsContent("invalidData"));
      } else {
        setError(errorsContent("technicalIssue"));
      }
    } finally {
      setIsPending(false);
    }
  }

  return {
    error,
    onSubmit,
    isPending,
    form,
    country,
    countryOptionnalLabels,
    exctractInfoForGuestCheckout,
  };
}
