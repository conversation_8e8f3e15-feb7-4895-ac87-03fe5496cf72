"use client";
import Link from "next/link";
import { buttonVariants } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import useLandingPage from "@/hooks/use-landing-page";
import { Skeleton } from "../ui/skeleton";

export default function LandingPageHeroSection() {
  const { isLoading, landingPageContent } = useLandingPage();

  return isLoading ? (
    <Skeleton className="h-[70vh] w-full" />
  ) : (
    landingPageContent && landingPageContent.images.length > 0 && (
      <div className="w-full relative">
        <div
          className="relative h-[70vh] flex items-center justify-start bg-cover bg-center"
          style={{
            backgroundImage: `url("${landingPageContent.images[3].computerImage}")`,
          }}
        >
          <div className=" absolute inset-0 bg-gradient-to-r from-black/60 to-black/30" />

          <div className="relative container px-4 mx-auto">
            <div className="max-w-lg space-y-6 text-white">
              <h1 className="text-4xl font-bold tracking-tight sm:text-5xl md:text-6xl">
                New Season Collection
              </h1>
              <p className="text-lg md:text-xl">
                Discover our latest styles crafted with premium materials for
                exceptional comfort and timeless elegance.
              </p>
              <div className="flex flex-wrap gap-4">
                <Link
                  href="/products"
                  className={cn(
                    buttonVariants({ variant: "default", size: "lg" })
                  )}
                >
                  Shop Now
                </Link>
                <Link
                  href="/products"
                  className={cn(
                    buttonVariants({ variant: "secondary", size: "lg" })
                  )}
                >
                  Shop Now
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  );
}
