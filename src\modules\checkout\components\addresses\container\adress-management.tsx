import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { AddressType } from "@/modules/checkout/types/addresses";
import { useTranslations } from "next-intl";

interface Props {
  address: AddressType;
  onDelete: (id: string) => void;
  bottomDelimter?: boolean;
}

export default function AddressManagementContainer({
  address,
  onDelete,
  bottomDelimter = true,
}: Props) {
  const t = useTranslations("accountPage.accountAdress.buttons");

  return (
    <div
      className={cn("py-3 border-gray border-opacity-50", {
        "border-b": bottomDelimter,
      })}
    >
      <p className="text-xl">{`${address.firstName} ${address.lastName}`}</p>
      <div className="flex justify-between">
        <p className="text-black  text-xl">
          {`${address.address1} ${address.city?.name} ${
            address.postalCode ? address.postalCode : ""
          }`}
        </p>
        <Button variant={"ghost"} onClick={() => onDelete(address.id)}>
          <p className="font-bold">{t("delete")}</p>
        </Button>
      </div>
    </div>
  );
}
