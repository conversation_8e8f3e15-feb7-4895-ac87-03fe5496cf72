import { useTranslations } from "next-intl";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import usePasswordSubmission from "../../hooks/reset-password/use-password-submission";

interface Props {
  email: string;
  code: string;
  onSuccess: () => void;
}

export default function NewPasswordSubmission({
  email,
  code,
  onSuccess,
}: Props) {
  const t = useTranslations("modules.auth.resetPassword");

  const { error, onSubmit, isPending, form } = usePasswordSubmission({
    email,
    code,
    onSuccess,
  });

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="flex flex-col gap-6"
      >
        <div className="flex flex-col items-center gap-2 text-center">
          <h1 className="text-2xl font-bold">{t("steps.newPassword.title")}</h1>
          <p className="text-muted-foreground text-sm text-balance">
            {t("steps.newPassword.description")}
          </p>
        </div>
        <div className="grid gap-6">
          <FormField
            control={form.control}
            name="password"
            render={({ field }) => (
              <FormItem>
                <FormLabel htmlFor="password">
                  {t("steps.newPassword.passwordLabel")}
                </FormLabel>
                <FormControl>
                  <Input
                    id="password"
                    type="password"
                    placeholder={t("steps.newPassword.passwordPlaceholder")}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {error && (
            <p className="text-sm text-destructive text-center">{error}</p>
          )}
          <Button type="submit" className="w-full" disabled={isPending}>
            {isPending
              ? t("steps.newPassword.button.loading")
              : t("steps.newPassword.button.default")}
          </Button>
        </div>
      </form>
    </Form>
  );
}
