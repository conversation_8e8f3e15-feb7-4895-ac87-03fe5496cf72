import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { cn } from "@/lib/utils";
import Text from "@/styles/text-styles";
import { useTranslations } from "next-intl";
import { useState } from "react";
import { ItemType, ProductType } from "../../../../types/products";
import { formatPrice } from "../../../../utils/prices-transformation";
import { Skeleton } from "@/components/ui/skeleton";
import Link from "next/link";
import { getBrandPageUrl } from "@/modules/catalog/utils/urls";

interface Props {
  product?: ProductType;
  productItem: ItemType | null;
}

export default function ProductDetails({ product, productItem }: Props) {
  const [selectedVariation, setSelectedVariation] = useState("");
  const t = useTranslations("productPage");
  const [brandImageNotFound, setBrandImageNotFound] = useState(false);
  const promotionIsAvailable =
    productItem?.prices[0].realPrice !==
    productItem?.prices[0].promotionalPrice;

  return product && productItem ? (
    <div className="space-y-6">
      {/* Product Name and barcode for reference section */}
      <div>
        <h1 className="font-bold text-primary">
          <Text textStyle="TS3">{product.name}</Text>
        </h1>
        {product.items[0].barcode && (
          <p className="text-gray">
            <Text textStyle="TS6">{`${t("productDetails.ref")} ${
              product.items[0].barcode
            }`}</Text>
          </p>
        )}
      </div>

      {/* Prices Section */}
      <div className="flex flex-col space-y-1">
        {promotionIsAvailable && (
          <Text textStyle="TS4" className="text-primary-dark line-through">
            {`${formatPrice(product.items[0].prices[0].realPrice)} ${
              product.items[0].prices[0].currency
            } `}
          </Text>
        )}
        <Text textStyle="TS3" className="text-primary font-bold">
          {`${product.items[0].prices[0].currency} ${formatPrice(
            product.items[0].prices[0].promotionalPrice
          )}`}
        </Text>
      </div>

      {product.description && (
        <div className="w-full flex flex-col L:space-y-5 space-y-3 text-gray">
          {/* <Text textStyle="TS4" className="text-primary font-bold">
            {t("productDetails.description")}
          </Text> */}
          <Text textStyle="TS6">{product.description}</Text>
        </div>
      )}

      {/* Product Brand Section */}
      {product.brand && (
        <div className="flex flex-col space-y-2">
          <Text textStyle="TS6" className="text-primary font-bold">
            {t.rich("brand", {
              underline: () =>
                product.brand ? (
                  <Link
                    href={getBrandPageUrl(product.brand.slug)}
                    className="font-normal underline"
                  >
                    {product.brand.name}
                  </Link>
                ) : null,
            })}
          </Text>
          {product.brand.image && !brandImageNotFound ? (
            product.brand.slug ? (
              <Link href={getBrandPageUrl(product.brand.slug)}>
                <img
                  src={product.brand.image || "/placeholder.svg"}
                  alt={product.brand.name}
                  className="L:h-[100px] h-20 w-full object-contain group-hover:scale-110 duration-200"
                  onError={() => setBrandImageNotFound(true)}
                />
              </Link>
            ) : (
              <img
                src={product.brand.image || "/placeholder.svg"}
                alt={product.brand.name}
                className="L:h-[100px] h-20 w-full object-contain group-hover:scale-110 duration-200"
                onError={() => setBrandImageNotFound(true)}
              />
            )
          ) : null}
        </div>
      )}

      {/* {product.description && (
        <div className="flex flex-col space-y-2">
          <Text textStyle="TS4" className="text-primary font-bold">
            {t("productDetails.description")}
          </Text>
          <div
            className={cn("text-gray prose prose-sm", TextStyle["TS6"])}
            dangerouslySetInnerHTML={{
              __html: DOMPurify.sanitize(product.description as string),
            }}
          ></div>
        </div>
      )} */}

      {/* Size Selector */}

      {productItem.variations.length > 0 && (
        <RadioGroup
          value={selectedVariation}
          onValueChange={setSelectedVariation}
          className="flex gap-4"
        >
          {productItem.variations.map((variation, idx) => (
            <label
              key={idx}
              className={cn(
                "px-6 py-2 border border-primary rounded-md cursor-pointer text-primary",
                selectedVariation === variation.value &&
                  " bg-primary text-white"
              )}
            >
              <RadioGroupItem value={variation.value} className="sr-only" />
              <Text textStyle="TS7">{`${variation.value}`}</Text>
            </label>
          ))}
        </RadioGroup>
      )}
    </div>
  ) : (
    <div className="space-y-6">
      <div className="space-y-1">
        <Skeleton className="h-10 w-48" />
        <Skeleton className="h-5 w-28" />
      </div>

      <div className="flex space-x-1 items-center">
        <Skeleton className="h-5 w-28" />
        <Skeleton className="h-5 w-28" />
      </div>

      <div className="text-gray space-y-1">
        {Array.from({ length: 4 }).map((_, idx: number) => (
          <Skeleton key={idx} className="h-5 w-full" />
        ))}
      </div>

      {/* Size Selector */}

      <div className="flex gap-4">
        {Array.from({ length: 2 }).map((_, idx) => (
          <Skeleton key={idx} className="h-10 w-20" />
        ))}
      </div>
    </div>
  );
}
