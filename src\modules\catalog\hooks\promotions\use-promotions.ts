import { keepPreviousData, useQuery } from "@tanstack/react-query";
import retrievePromotionsFromServerSide from "../../services/promotions/promotions-extraction";
import useBackendLocale from "@/hooks/use-backend-locale";

export default function usePromotions() {
  const { backendLocale } = useBackendLocale();
  const { data, isLoading } = useQuery({
    queryKey: ["promotions", backendLocale],
    queryFn: () => retrievePromotionsFromServerSide({ locale: backendLocale }),
    placeholderData: keepPreviousData,
  });

  return {
    promotions: data,
    promotionsAreLoading: isLoading,
  };
}
