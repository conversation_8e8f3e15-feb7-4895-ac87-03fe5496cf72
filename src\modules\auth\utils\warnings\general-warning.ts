export function getServerErrorWarning(
  status: number,
  t: (key: string) => string
) {}

export function getPasswordChangementGeneralWarning(
  passwords: {
    currentPassword: string;
    newPassword: string;
    confirmationPassword: string;
  },
  t: (key: string) => string
) {
  if (
    passwords.currentPassword.length < 8 ||
    passwords.newPassword.length < 8 ||
    passwords.confirmationPassword.length < 8
  )
    return t("warnings.passwordMinLengthError");

  if (passwords.newPassword !== passwords.confirmationPassword)
    return t("warnings.passwordsDoNotMatchError");

  return "";
}
