import { useState } from "react";
import { useTranslations } from "next-intl";
import { useQueryClient } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import z from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { CustomError } from "@/utils/custom-error";
import { getSignUpFormSchema } from "../validation/schemas/sign-up";
import { signUp } from "../services/sign-up";
import { addCartItemsOnServerSide } from "@/modules/cart/services/cart-items-addition";
import { useCartStore } from "@/modules/cart/store/cart-store";
import useAuthRefresher from "../store/auth-refresher";

export default function useSignUp() {
  const queryClient = useQueryClient();
  const router = useRouter();
  const t = useTranslations("modules.auth.validations");
  const errorsContent = useTranslations("modules.auth.errors");

  const [isPending, setIsPending] = useState(false);
  const [error, setError] = useState("");

  const { refreshUserAuthentication } = useAuthRefresher((store) => store);
  const cartItems = useCartStore((store) => store.state.cartItems);

  const formSchema = getSignUpFormSchema(t);
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      firstName: "",
      lastName: "",
      email: "",
      password: "",
    },
  });

  async function onSubmit(values: z.infer<typeof formSchema>) {
    if (error !== "") setError("");

    try {
      await signUp({
        firstName: values.firstName,
        lastName: values.lastName,
        email: values.email,
        password: values.password,
      });

      const cartItemsToAdd = cartItems.map((cartItem) => ({
        id: cartItem.id,
        quantity: cartItem.cartQuantity,
      }));

      addCartItemsOnServerSide(cartItemsToAdd).catch(() => {});

      //section changemennt if we've account confirmation phase

      //refresh user authentication state
      refreshUserAuthentication();
      queryClient.invalidateQueries({ queryKey: ["user-data"] });

      setIsPending(false);
      router.push("/");
    } catch (e) {
      const error = e as CustomError;

      if (error.status === 400) {
        setError(errorsContent("emailAlreadyExist"));
      } else {
        setError(errorsContent("technicalIssue"));
      }
    } finally {
      setIsPending(false);
    }
  }

  return { error, onSubmit, isPending, form };
}
