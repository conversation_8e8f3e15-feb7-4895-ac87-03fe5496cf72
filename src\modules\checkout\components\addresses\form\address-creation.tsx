"use client";
import useCountries from "@/modules/checkout/hooks/addresses/use-countries";
import { cn } from "@/lib/utils";
import { Input } from "@/components/ui/input";
import AddressDropdownMenu from "../address-dropdown-menu";
import useCountryCities from "@/modules/checkout/hooks/addresses/use-country-cities";
import { useTranslations } from "next-intl";
import { Label } from "@/components/ui/label";
import { FormEvent, Ref } from "react";
import { Button } from "@/components/ui/button";
import PhoneNumberInput from "@/components/phone-number-input";

interface AddressCreationPropsType {
  formIsHidden: boolean;
  wrongInputs: string[];
  warning: string;
  formRef: Ref<HTMLFormElement>;
  primaryTheme?: boolean;
  onCreate?: (event: FormEvent) => void;
  isLoading?: boolean;
}

export default function AddressCreation({
  formIsHidden,
  wrongInputs,
  warning,
  formRef,
  primaryTheme = true,
  onCreate,
  isLoading = false,
}: AddressCreationPropsType) {
  const t = useTranslations("modules.checkout.adressess");
  const buttonsContent = useTranslations("modules.checkout.adressess.actions");
  const { country, countryOptionnalLabels } = useCountries();
  const { cities, city, changeCity } = useCountryCities({
    countryCode: country?.code as string,
  });

  return country ? (
    <form
      id="addressCreation"
      ref={formRef}
      className={cn("flex flex-col space-y-3 text-primary", {
        hidden: formIsHidden,
      })}
    >
      {warning !== "" ? <p className="text-red">{warning}</p> : null}
      <Input
        name={"email"}
        className={cn("XL:h-[60px] L:h-[50px] h-10 bg-opacity-55 border", {
          "border-danger": wrongInputs.includes("email"),
          "border-primary": !wrongInputs.includes("email") && primaryTheme,
        })}
        placeholder="Adresse électronique"
      />
      {country.formatting.show.split("_").map((inputs, idx) => {
        const inputsLabels = inputs.split(" ");

        return (
          <div key={idx} className={"flex space-x-2"}>
            {inputsLabels.map((input) => (
              <div key={input} className="w-full">
                <Label
                  className={cn("h-full flex flex-col space-y-1", {
                    "basis-1/2": inputsLabels.length > 1,
                  })}
                >
                  {input.substring(1, input.length - 1) ===
                  "country" ? null : country.code === "TN" &&
                    input.substring(1, input.length - 1) === "city" &&
                    cities.length > 0 ? (
                    <AddressDropdownMenu
                      data={cities}
                      selectedElement={city}
                      onChange={changeCity}
                      primaryTheme={false}
                    />
                  ) : input.substring(1, input.length - 1) === "phone" ? (
                    <PhoneNumberInput
                      inputName={input.substring(1, input.length - 1)}
                      code={`+${country.phoneNumberPrefix}`}
                      className={cn(
                        "XL:h-[60px] L:h-[50px] h-10 bg-primary-muted bg-opacity-55 border",
                        {
                          "border-danger": wrongInputs.includes(
                            input.substring(1, input.length - 1)
                          ),
                          "border-primary":
                            !wrongInputs.includes(
                              input.substring(1, input.length - 1)
                            ) && primaryTheme,
                        }
                      )}
                      primaryTheme={false}
                    />
                  ) : (
                    <Input
                      name={input.substring(1, input.length - 1)}
                      className={cn(
                        "XL:h-[60px] L:h-[50px] h-10 bg-opacity-55 border",
                        {
                          "border-danger": wrongInputs.includes(
                            input.substring(1, input.length - 1)
                          ),
                          "border-primary":
                            !wrongInputs.includes(
                              input.substring(1, input.length - 1)
                            ) && primaryTheme,
                        }
                      )}
                      placeholder={`${input.substring(1, input.length - 1)} ${
                        !countryOptionnalLabels.includes(
                          input.substring(1, input.length - 1)
                        )
                          ? "*"
                          : t("optional")
                      }`}
                    />
                  )}
                </Label>
              </div>
            ))}
          </div>
        );
      })}
      {onCreate && (
        <Button
          onClick={(event) => onCreate(event)}
          className={cn("rounded-[10px] h-fit", {
            "scale-95 opacit-70": isLoading,
          })}
          disabled={isLoading}
        >
          {buttonsContent("confirm")}
        </Button>
      )}
    </form>
  ) : null;
}
