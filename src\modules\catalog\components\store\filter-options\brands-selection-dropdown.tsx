import { Checkbox } from "@/components/ui/checkbox";
import useBrands from "@/modules/catalog/hooks/brands/use-brands";
import {
  getSelectedCategories,
  useProductsFilteringStore,
} from "@/modules/catalog/store/products-filter";
import { BrandType } from "@/modules/catalog/types/brands";
import { useTranslations } from "next-intl";
import FilterChoiceContainer from "./filter-choice-container";
import Image from "next/image";
import { CheckedState } from "@radix-ui/react-checkbox";
import { useEffect, useState } from "react";
import Text from "@/styles/text-styles";
import { cn } from "@/lib/utils";

export default function BrandsSelectionDropDown() {
  const t = useTranslations("filtersPage");
  const { priceRange, search } = useProductsFilteringStore((store) => store);
  const { brands, brandsAreLoading } = useBrands({
    limit: 500,
    categoriesSlugs: getSelectedCategories().map((cat) => cat.slug),
    productPriceRange: priceRange,
    searchByProductName: search,
  });

  return (
    brands?.length > 0 && (
      <FilterChoiceContainer title={t("brandsHeader")}>
        {brands.map((brand) => (
          <BrandSelection key={brand.id} brand={brand} />
        ))}
      </FilterChoiceContainer>
    )
  );
}

function BrandSelection({ brand }: { brand: BrandType }) {
  const [checked, setChecked] = useState<CheckedState>(false);
  const [brandImage, setBrandImage] = useState(
    brand.image ? brand.image : "/not-found/product-image.webp"
  );
  const { addBrand, removeBrand, applyFilter, selectedBrands } =
    useProductsFilteringStore();

  const handleBrandClick = (chooseBrand: BrandType, checked: CheckedState) => {
    if (!checked) {
      removeBrand(chooseBrand.id);
    } else {
      addBrand(chooseBrand);
    }

    applyFilter();
    setChecked(checked);
  };

  useEffect(() => {
    if (selectedBrands.find((selectedBrand) => selectedBrand.id === brand.id))
      setChecked(true);
  }, [selectedBrands]);

  return (
    <div className="flex items-center space-x-3">
      <Checkbox
        id={brand.id}
        className=""
        checked={checked}
        onCheckedChange={(checked: CheckedState) =>
          handleBrandClick(brand, checked)
        }
      />

      <label
        htmlFor={brand.id}
        className={cn("text-sm text-gray cursor-pointer flex space-x-2", {})}
      >
        <Image
          width={30}
          height={30}
          alt={brand.name}
          src={brandImage}
          className={cn({})}
          onError={() => setBrandImage("/not-found/product-image.webp")}
        />
        <Text textStyle="TS7">{brand.name}</Text>
      </label>
    </div>
  );
}
