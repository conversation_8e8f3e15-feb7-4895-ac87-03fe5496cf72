import { useEffect } from "react";
import usePagination from "@/hooks/use-pagination";
import { keepPreviousData, useQuery } from "@tanstack/react-query";
import { ProductType } from "../../types/products";
import { PaginationType } from "@/types";
import { retrieveSimilarProductsFromServerSide } from "../../services/products/similar-products-extraction";
import { CriteriaType } from "../../types";
import { retrieveProductsFromServerSide } from "../../services/products/products-extraction";
import useBackendLocale from "@/hooks/use-backend-locale";

interface Params {
  limit: number;
  criteria?: CriteriaType;
  similarProductSlug?: string;
  categoriesSlugs?: string[];
  brandSlugs?: string[];
  queryKeys?: (string | number)[];
  search?: string;
}

export default function useProducts({
  limit,
  criteria,
  brandSlugs,
  similarProductSlug,
  categoriesSlugs,
  search,
  queryKeys = [],
}: Params) {
  const { backendLocale } = useBackendLocale();

  const { page, setPage, pagesNumber, setPagesNumber, paginatedListRef } =
    usePagination();

  const { data, isLoading } = useQuery<{
    products: ProductType[];
    pagination: PaginationType;
  } | null>({
    queryKey: [
      "products",
      page,
      criteria,
      similarProductSlug,
      categoriesSlugs,
      brandSlugs,
      search,
      backendLocale,
      ...queryKeys,
    ],
    queryFn: () =>
      similarProductSlug
        ? retrieveSimilarProductsFromServerSide({
            page,
            limit,
            slug: similarProductSlug,
            locale: backendLocale,
          })
        : retrieveProductsFromServerSide({
            page,
            limit,
            brandSlugs,
            categoriesSlugs,
            criteria,
            search,
            locale: backendLocale,
          }),
    placeholderData: keepPreviousData,
  });

  useEffect(() => {
    setPagesNumber(
      data?.pagination?.totalPages ? data?.pagination?.totalPages : 1
    );
  }, [data]);

  return {
    products: data?.products,
    productsAreLoading: isLoading,
    setPage,
    page,
    pagesNumber,
    paginatedListRef,
  };
}
