import * as React from "react";
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import { cn } from "@/lib/utils";

interface PaginationManagementProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  scrollToTop?: boolean;
  showPreviousNext?: boolean;
  maxVisiblePages?: number;
  className?: string;
}

export default function PaginationManagement({
  currentPage,
  totalPages,
  onPageChange,
  scrollToTop = true,
  showPreviousNext = true,
  maxVisiblePages = 5,
  className,
}: PaginationManagementProps) {
  // Scroll to top when page changes
  React.useEffect(() => {
    if (scrollToTop) {
      window.scrollTo({
        top: 0,
        behavior: "smooth",
      });
    }
  }, [currentPage, scrollToTop]);

  // Don't render if only one page
  if (totalPages <= 1) return null;

  // Generate page numbers to display
  const generatePageNumbers = () => {
    const pages: (number | "ellipsis")[] = [];

    if (totalPages <= maxVisiblePages) {
      // Show all pages if total is less than max visible
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      // Always show first page
      pages.push(1);

      // Calculate start and end of middle section
      const startPage = Math.max(
        2,
        currentPage - Math.floor((maxVisiblePages - 3) / 2)
      );
      const endPage = Math.min(totalPages - 1, startPage + maxVisiblePages - 3);

      // Add ellipsis after first page if needed
      if (startPage > 2) {
        pages.push("ellipsis");
      }

      // Add middle pages
      for (let i = startPage; i <= endPage; i++) {
        pages.push(i);
      }

      // Add ellipsis before last page if needed
      if (endPage < totalPages - 1) {
        pages.push("ellipsis");
      }

      // Always show last page
      if (totalPages > 1) {
        pages.push(totalPages);
      }
    }

    return pages;
  };

  const pageNumbers = generatePageNumbers();

  const handlePageClick = (page: number) => {
    if (page >= 1 && page <= totalPages && page !== currentPage) {
      onPageChange(page);
    }
  };

  const handlePrevious = () => {
    if (currentPage > 1) {
      onPageChange(currentPage - 1);
    }
  };

  const handleNext = () => {
    if (currentPage < totalPages) {
      onPageChange(currentPage + 1);
    }
  };

  return (
    <Pagination className={className}>
      <PaginationContent>
        {/* Previous Button */}
        {showPreviousNext && (
          <PaginationItem>
            <PaginationPrevious
              onClick={handlePrevious}
              className={cn(
                currentPage <= 1 && "pointer-events-none opacity-50"
              )}
            />
          </PaginationItem>
        )}

        {/* Page Numbers */}
        {pageNumbers.map((page, index) => (
          <PaginationItem key={index}>
            {page === "ellipsis" ? (
              <PaginationEllipsis />
            ) : (
              <PaginationLink
                onClick={() => handlePageClick(page)}
                isActive={page === currentPage}
                className="min-w-[2.25rem]"
              >
                {page}
              </PaginationLink>
            )}
          </PaginationItem>
        ))}

        {/* Next Button */}
        {showPreviousNext && (
          <PaginationItem>
            <PaginationNext
              onClick={handleNext}
              className={cn(
                currentPage >= totalPages && "pointer-events-none opacity-50"
              )}
            />
          </PaginationItem>
        )}
      </PaginationContent>
    </Pagination>
  );
}
