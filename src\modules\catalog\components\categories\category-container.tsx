import Image from "next/image";
import Link from "next/link";
import type { CategoryType } from "../../types/categories";
import { getCategoryPageUrl } from "@/utils/urls";

interface Props {
  category: CategoryType;
}

export default function CategoryContainer({ category }: Props) {
  return (
    category && (
      <Link href={getCategoryPageUrl(category)} className="group block">
        <div className="relative w-fit overflow-hidden rounded-full shadow-sm transition-transform duration-300 group-hover:scale-105">
          <Image
            unoptimized
            height={150}
            width={150}
            alt={category.name}
            src={category.image || "/not-found/product-image.webp"}
            className="h-[150px] w-[150px] object-cover"
          />
          <div className="absolute inset-0 flex flex-col items-center justify-center bg-primary/50 transition-all duration-700 group-hover:bg-primary">
            <h3 className="text-center text-lg font-bold text-primary-foreground">
              {category.name}
            </h3>
          </div>
        </div>
      </Link>
    )
  );
}
