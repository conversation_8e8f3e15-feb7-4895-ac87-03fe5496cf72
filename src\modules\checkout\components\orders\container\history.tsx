"use client";
import { OrderDataType } from "../../../types/orders";
import { useTranslations } from "use-intl";
import HistoryOrderItemContainer from "../history-order-item-container";
import useCurrency from "@/modules/catalog/hooks/use-currency";
import { cn } from "@/lib/utils";
import { Separator } from "@/components/ui/separator";

type Props = {
  details: OrderDataType;
};

const OrderHistoryContainer = ({ details }: Props) => {
  const t = useTranslations("ordersManagement");
  const { currency } = useCurrency();

  return (
    <div className="L:min-w-[400px] XL:min-w-[600px] w-full space-y-4  h-fit flex flex-col bg-primary/10 XL:rounded-[33px] L:rounded-[25px] rounded-[18px] text-primary  border border-primary justify-between">
      <div className="flex justify-between bg-primary text-white XL:p-5 L:p-4 p-3 xl:rounded-tl-[30px] xl:rounded-tr-[30px] l:rounded-tl-[25px] l:rounded-tr-[25px] rounded-tl-[15px] rounded-tr-[15px]">
        <div className="flex flex-col space-y-2">
          <h3 className="block text-lg">{t("order.labels.number")}</h3>
          <p className="font-bold block text-lg">#{details.code}</p>
        </div>
        <div className="flex flex-col space-y-2">
          <h3 className="block text-lg">{t("order.labels.total")}</h3>
          <p className="font-bold block text-lg">
            {`${(
              details.total +
              details.shippingCost -
              details.discount
            ).toFixed(3)} ${currency}`}
          </p>
        </div>
      </div>
      <div className="XL:p-6 L:p-5 p-4 flex flex-col space-y-7">
        {details.items.map((item, idx) => (
          <HistoryOrderItemContainer key={idx} item={item} />
        ))}

        <div className="flex flex-col space-y-3">
          <div className="flex justify-between">
            <h4 className="font-bold text-black text-lg">
              {details.discount !== 0
                ? t("order.labels.subTotalWithoutDiscount")
                : t("order.labels.subTotal")}
              :
            </h4>
            <span className="text-base">{`${details.total.toFixed(
              3
            )} ${currency}`}</span>
          </div>

          {details.discount !== 0 && (
            <div className="flex justify-between">
              <h4 className="font-bold text-black text-lg">
                {t("order.labels.discount")}:
              </h4>
              <span className="text-base">{`${details.discount.toFixed(
                3
              )} ${currency}`}</span>
            </div>
          )}

          <div className="flex justify-between">
            <h4 className="font-bold text-black text-lg">
              {t("order.labels.shippingCost")}:
            </h4>
            <span className="text-base">{`${details.shippingCost.toFixed(
              3
            )} ${currency}`}</span>
          </div>

          <Separator />

          <div className="flex justify-between">
            <h4 className="font-bold text-black text-lg">
              {details.discount !== 0
                ? t("order.labels.subTotalWithDiscount")
                : t("order.labels.total")}
              :
            </h4>
            <span className="text-base">
              {`${(
                details.total +
                details.shippingCost -
                details.discount
              ).toFixed(3)} ${currency}`}
            </span>
          </div>
        </div>

        <div className="space-y-2">
          <div className="flex flex-wrap items-center gap-1">
            <span className="block md:inline">
              {t.rich("order.labels.ProcessingText")}
            </span>
            <span
              className={cn(
                "font-normal text-secondary bg-secondary-light border border-secondary rounded-3xl block py-1 px-3 w-fit md:py-1 md:px-3 text-base",
                {
                  "bg-[#FFF3E4] text-[#8F4917] border-[#8F4917]":
                    "Returned" == details.status,
                  "text-primary bg-primary-light border-primary":
                    details.status === "Delivered",
                  "bg-[#F7F9FC] text-gray border-gray":
                    details.status === "Cancelled",
                  "bg-[#FFCBCB] text-[#F90000] border-[#F90000]": [
                    "Failed",
                  ].includes(details.status),
                }
              )}
            >
              {t(`orderStatus.${details.status}`)}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OrderHistoryContainer;
