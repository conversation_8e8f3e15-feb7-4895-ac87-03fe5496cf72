"use client";
import { useTranslations } from "next-intl";
import type { PromotionType } from "@/modules/catalog/types";
import usePromotionProducts from "@/modules/catalog/hooks/promotions/use-products";
import { HTMLAttributes } from "react";
import ProductsOverviewUI from "./ui";
import { useWindowWidth } from "@react-hook/window-size";
import { getPromotionPageUrl } from "@/utils/urls";

interface Props extends HTMLAttributes<"div"> {
  promotion: PromotionType;
}

export default function PromotionProductsOverview({ promotion }: Props) {
  const t = useTranslations("modules.catalog.products.overview");
  const screenWidth = useWindowWidth();

  const { products, productsAreLoading } = usePromotionProducts({
    limit: screenWidth < 768 ? 8 : 7,
    promotionSlug: promotion ? promotion.slug : "",
    queryKeys: [screenWidth],
  });

  const moreProductsLink = getPromotionPageUrl(promotion.slug);

  return (
    <ProductsOverviewUI
      title={t("specialOffers.title")}
      subtitle={t("specialOffers.subtitle")}
      products={products}
      isLoading={productsAreLoading}
      checkMoreButton={{
        content: t("buttons.discoverPlus"),
        link: moreProductsLink,
      }}
    />
  );
}
